package com.trading.bot.strategy;

import com.trading.bot.analysis.TechnicalAnalyzer;
import com.trading.bot.analysis.SupportResistanceDetector;
import com.trading.bot.analysis.VegasChannelAnalyzer;
import com.trading.bot.config.TradingConfig;
import com.trading.bot.entity.Position;
import com.trading.bot.service.BinanceApiService;
import com.trading.bot.service.TradingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class MA25TradingStrategy {

    private static final Logger logger = LoggerFactory.getLogger(MA25TradingStrategy.class);

    @Autowired
    private BinanceApiService binanceApiService;

    @Autowired
    private TradingService tradingService;

    @Autowired
    private TechnicalAnalyzer technicalAnalyzer;

    @Autowired
    private TradingConfig tradingConfig;
    
    @Autowired
    private SupportResistanceDetector supportResistanceDetector;
    
    @Autowired
    private VegasChannelAnalyzer vegasChannelAnalyzer;

    private static final BigDecimal PROFIT_THRESHOLD_PERCENTAGE = new BigDecimal("0.005"); // 0.5% 利润阈值
    
    // 新增：手续费计算常量
    private static final BigDecimal TRADING_FEE_RATE = new BigDecimal("0.0004"); // 0.04% 手续费率（双向0.08%）
    private static final BigDecimal MIN_PROFIT_AFTER_FEE = new BigDecimal("0.015"); // 最小净利润1.5%（覆盖手续费+小利润）

    /**
     * 计算扣除手续费后的净利润率
     * @param position 持仓信息
     * @param currentPrice 当前价格
     * @return 净利润率（百分比）
     */
    private BigDecimal calculateNetProfitPercentage(Position position, BigDecimal currentPrice) {
        if (position.getTotalAmount() == null || position.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        
        // 计算未实现盈亏
        BigDecimal unrealizedPnl = position.calculatePnl(currentPrice);
        
        // 计算总手续费：开仓手续费 + 平仓手续费
        BigDecimal totalPositionValue = position.getQuantity().multiply(currentPrice);
        BigDecimal openFee = position.getTotalAmount().multiply(TRADING_FEE_RATE); // 开仓手续费
        BigDecimal closeFee = totalPositionValue.multiply(TRADING_FEE_RATE); // 平仓手续费
        BigDecimal totalFees = openFee.add(closeFee);
        
        // 净利润 = 未实现盈亏 - 总手续费
        BigDecimal netProfit = unrealizedPnl.subtract(totalFees);
        
        // 净利润率 = 净利润 / 总投入金额
        BigDecimal netProfitPercentage = netProfit.divide(position.getTotalAmount(), 4, RoundingMode.HALF_UP);
        
        return netProfitPercentage;
    }

    /**
     * 检查是否满足最小净利润要求（扣除手续费后）
     * @param position 持仓信息
     * @param currentPrice 当前价格
     * @return 是否满足最小净利润
     */
    private boolean isProfitableAfterFees(Position position, BigDecimal currentPrice) {
        BigDecimal netProfitPercentage = calculateNetProfitPercentage(position, currentPrice);
        return netProfitPercentage.compareTo(MIN_PROFIT_AFTER_FEE) >= 0;
    }

    /**
     * 检查价格是否连续站稳在MA之上（核心调头判断逻辑）
     * 技术要点：只要K线实体收在MA之上即算有效，下影线刺破MA下方不影响判断
     * 🔥 新增：检查第一根站稳MA的K线距离，避免追高
     * @param klines K线数据
     * @param maValue MA值
     * @param confirmationPeriods 连续确认的K线数量（通常为3）
     * @param symbol 交易对符号（用于日志）
     * @return 是否连续站稳MA之上且第一根距离合理
     */
    public boolean isPriceConsecutivelyAboveMA(List<List<String>> klines, BigDecimal maValue, int confirmationPeriods, String symbol) {
        // 🔥 重构：先调用TechnicalAnalyzer的基础方法
        boolean basicCheck = technicalAnalyzer.isPriceConsecutivelyAboveMA(klines, maValue, confirmationPeriods);
        if (!basicCheck) {
            return false;
        }

        // 🔥 策略层风险控制：检查第一根站稳MA的K线距离是否合理
        if (klines.size() < confirmationPeriods) {
            return false;
        }

        // 获取第一根站稳的K线（最远的那根）
        List<String> firstStandingKline = klines.get(klines.size() - confirmationPeriods);
        BigDecimal firstStandingKlineClose = new BigDecimal(firstStandingKline.get(4)); // 收盘价
        
        // 检查距离是否合理（避免追高）
        BigDecimal firstKlineDistance = firstStandingKlineClose.subtract(maValue).divide(maValue, 4, RoundingMode.HALF_UP);
        BigDecimal maxReasonableDistance = new BigDecimal("0.02"); // 第一根站稳时距离不超过2%
        
        if (firstKlineDistance.compareTo(maxReasonableDistance) > 0) {
            BigDecimal distancePercent = firstKlineDistance.multiply(new BigDecimal("100"));
            logger.info("[{}] 开多信号被拒绝：第一根站稳MA25的K线距离过远 | 第一根收盘价: {} 距离MA25: +{}% > 2% | 策略: 避免追高", 
                       symbol, firstStandingKlineClose.toPlainString(), distancePercent.toPlainString());
            return false;
        } else {
            BigDecimal distancePercent = firstKlineDistance.multiply(new BigDecimal("100"));
            logger.debug("[{}] 第一根站稳MA25的K线距离合理 | 收盘价: {} 距离MA25: +{}% <= 2%", 
                        symbol, firstStandingKlineClose.toPlainString(), distancePercent.toPlainString());
        }
        
        logger.debug("[{}] 价格连续{}根K线实体收在MA{}之上（影线刺破不影响判断）且第一根距离合理", symbol, confirmationPeriods, maValue.toPlainString());
        return true;
    }

    /**
     * 🔥 新增：检查刚好突破MA25的连续K线（优化版）
     * @param klines K线数据
     * @param maValue MA值
     * @param confirmationPeriods 确认周期数
     * @param symbol 交易对符号
     * @return 是否刚好突破
     */
    public boolean isPriceJustBreakthroughAboveMA(List<List<String>> klines, BigDecimal maValue, int confirmationPeriods, String symbol) {
        if (klines.size() < confirmationPeriods) {
            return false;
        }

        // 检查最近N根K线的收盘价是否刚好突破MA25
        for (int i = 0; i < confirmationPeriods; i++) {
            BigDecimal klineClose = new BigDecimal(klines.get(klines.size() - 1 - i).get(4));
            if (klineClose.compareTo(maValue) <= 0) {
                return false; // 任何一根K线收盘价不在MA25上方，就不是刚好突破
            }
        }

        // 🔥 优化策略：使用动态距离检测，避免错过突破信号
        BigDecimal firstBreakingKlineClose = new BigDecimal(klines.get(klines.size() - confirmationPeriods).get(4));
        BigDecimal firstKlineDistance = firstBreakingKlineClose.subtract(maValue).divide(maValue, 4, RoundingMode.HALF_UP);
        
        // 🔥 严格风控：第一根突破K线距离不超过2%，避免追多
        BigDecimal maxReasonableDistance = new BigDecimal("0.02"); // 第一根突破时距离不超过2.0%
        
        if (firstKlineDistance.compareTo(maxReasonableDistance) > 0) {
            BigDecimal distancePercent = firstKlineDistance.multiply(new BigDecimal("100"));
            logger.info("[{}] 开多信号被拒绝：第一根突破MA25的K线距离过远 | 第一根收盘价: {} 距离MA25: +{}% > 2.0% | 策略: 避免追多", 
                       symbol, firstBreakingKlineClose.toPlainString(), distancePercent.toPlainString());
            return false;
        }

        // 🔥 智能风险控制检查
        BigDecimal currentPrice = new BigDecimal(klines.get(klines.size() - 1).get(4));
        BigDecimal currentPriceDistanceFromMA = currentPrice.subtract(maValue)
            .divide(maValue, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        
        // 1. 检查突破时机是否新鲜（避免追多已经涨很久的）
        int maxRecentBreakKlines = 5; // 最多允许5根K线前的突破（1.25小时内）
        boolean isRecentBreak = true;
        
        // 检查前N根K线，如果太多根都在MA25上方，说明突破很久了
        int klinesBelowMA = 0;
        int checkRange = Math.min(maxRecentBreakKlines, klines.size() - confirmationPeriods);
        
        for (int i = confirmationPeriods; i < confirmationPeriods + checkRange; i++) {
            BigDecimal checkKlineClose = new BigDecimal(klines.get(klines.size() - 1 - i).get(4));
            if (checkKlineClose.compareTo(maValue) <= 0) {
                klinesBelowMA++;
                break; // 找到第一根在MA25下方的K线，说明突破还算新鲜
            }
        }
        
        if (klinesBelowMA == 0 && checkRange >= maxRecentBreakKlines) {
            logger.info("[{}] 开多信号被拒绝：突破时机过旧，连续{}根以上K线都在MA25上方，避免追多", 
                       symbol, checkRange + confirmationPeriods);
            return false;
        }
        
        // 2. 检查是否刚从支撑位突破（这是开多的好时机）- 🔥 使用多时间周期
        boolean hasSupportBreakthrough = false;
        try {
            BigDecimal supportPrice = supportResistanceDetector.getMultiTimeframeSupport(symbol, currentPrice);
            if (supportPrice != null && supportPrice.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal supportDistance = currentPrice.subtract(supportPrice).divide(currentPrice, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));

                // 如果当前价格在支撑位上方5%内，说明刚从支撑位突破
                if (supportDistance.compareTo(new BigDecimal("5.0")) >= 0 && supportDistance.compareTo(new BigDecimal("15.0")) <= 0) {
                    hasSupportBreakthrough = true;
                    logger.info("[{}] 检测到支撑位{}突破：当前价格距离支撑位+{}%，有利于开多",
                               symbol, supportPrice.toPlainString(), supportDistance.toPlainString());
                }
            }
        } catch (Exception e) {
            logger.debug("[{}] 支撑位检测异常，忽略: {}", symbol, e.getMessage());
        }
        
        // 3. 动态距离控制：如果有支撑位突破，可以容忍更大距离
        BigDecimal maxDistanceThreshold = hasSupportBreakthrough ? new BigDecimal("12.0") : new BigDecimal("6.0");
        
        if (currentPriceDistanceFromMA.compareTo(maxDistanceThreshold) > 0) {
            logger.info("[{}] 开多信号被拒绝：当前价格距离MA25过远+{}%>{}%，避免极端追多", 
                       symbol, currentPriceDistanceFromMA.toPlainString(), maxDistanceThreshold.toPlainString());
            return false;
        }
        
        BigDecimal distancePercent = firstKlineDistance.multiply(new BigDecimal("100"));
        String freshness = klinesBelowMA > 0 ? "新鲜突破" : "延续上涨";
        String supportInfo = hasSupportBreakthrough ? "有支撑位突破支持" : "无明显支撑位参考";
        
        logger.info("[{}] 价格突破MA25确认：连续{}根K线收盘价在MA25上方，第一根距离+{}%合理，当前距离MA25+{}%，{}-{}，最大容忍距离{}%", 
                   symbol, confirmationPeriods, distancePercent.toPlainString(), currentPriceDistanceFromMA.toPlainString(),
                   freshness, supportInfo, maxDistanceThreshold.toPlainString());
        return true;
    }

    /**
     * 🔥 新增：检查刚好跌破MA25的连续K线（优化版）
     * @param klines K线数据
     * @param maValue MA值
     * @param confirmationPeriods 确认周期数
     * @param symbol 交易对符号
     * @return 是否刚好跌破
     */
    public boolean isPriceJustBreakthroughBelowMA(List<List<String>> klines, BigDecimal maValue, int confirmationPeriods, String symbol) {
        if (klines.size() < confirmationPeriods) {
            return false;
        }

        // 检查最近N根K线的收盘价是否刚好跌破MA25
        for (int i = 0; i < confirmationPeriods; i++) {
            BigDecimal klineClose = new BigDecimal(klines.get(klines.size() - 1 - i).get(4));
            if (klineClose.compareTo(maValue) >= 0) {
                return false; // 任何一根K线收盘价不在MA25下方，就不是刚好跌破
            }
        }

        // 🔥 优化策略：使用动态距离检测，避免错过跌破信号
        BigDecimal firstBreakingKlineClose = new BigDecimal(klines.get(klines.size() - confirmationPeriods).get(4));
        BigDecimal firstKlineDistance = maValue.subtract(firstBreakingKlineClose).divide(maValue, 4, RoundingMode.HALF_UP);
        
        // 🔥 严格风控：第一根跌破K线距离不超过2%，避免追空
        BigDecimal maxReasonableDistance = new BigDecimal("0.02"); // 第一根跌破时距离不超过2.0%
        
        if (firstKlineDistance.compareTo(maxReasonableDistance) > 0) {
            BigDecimal distancePercent = firstKlineDistance.multiply(new BigDecimal("100"));
            logger.info("[{}] 开空信号被拒绝：第一根跌破MA25的K线距离过远 | 第一根收盘价: {} 距离MA25: -{}% > 2.0% | 策略: 避免追空", 
                       symbol, firstBreakingKlineClose.toPlainString(), distancePercent.toPlainString());
            return false;
        }

        // 🔥 智能风险控制检查
        BigDecimal currentPrice = new BigDecimal(klines.get(klines.size() - 1).get(4));
        BigDecimal currentPriceDistanceFromMA = maValue.subtract(currentPrice)
            .divide(maValue, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        
        // 1. 检查跌破时机是否新鲜（避免追空已经跌很久的）
        int maxRecentBreakKlines = 5; // 最多允许5根K线前的跌破（1.25小时内）
        boolean isRecentBreak = true;
        
        // 检查前N根K线，如果太多根都在MA25下方，说明跌破很久了
        int klinesAboveMA = 0;
        int checkRange = Math.min(maxRecentBreakKlines, klines.size() - confirmationPeriods);
        
        for (int i = confirmationPeriods; i < confirmationPeriods + checkRange; i++) {
            BigDecimal checkKlineClose = new BigDecimal(klines.get(klines.size() - 1 - i).get(4));
            if (checkKlineClose.compareTo(maValue) >= 0) {
                klinesAboveMA++;
                break; // 找到第一根在MA25上方的K线，说明跌破还算新鲜
            }
        }
        
        if (klinesAboveMA == 0 && checkRange >= maxRecentBreakKlines) {
            logger.info("[{}] 开空信号被拒绝：跌破时机过旧，连续{}根以上K线都在MA25下方，避免追空", 
                       symbol, checkRange + confirmationPeriods);
            return false;
        }
        
        // 2. 检查是否刚从阻力位跌破（这是开空的好时机）- 🔥 使用多时间周期
        boolean hasResistanceBreakthrough = false;
        try {
            BigDecimal resistancePrice = supportResistanceDetector.getMultiTimeframeResistance(symbol, currentPrice);
            if (resistancePrice != null && resistancePrice.compareTo(BigDecimal.valueOf(Double.MAX_VALUE)) < 0) {
                BigDecimal resistanceDistance = resistancePrice.subtract(currentPrice).divide(currentPrice, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));

                // 如果当前价格在阻力位下方5%内，说明刚从阻力位跌破
                if (resistanceDistance.compareTo(new BigDecimal("5.0")) >= 0 && resistanceDistance.compareTo(new BigDecimal("15.0")) <= 0) {
                    hasResistanceBreakthrough = true;
                    logger.info("[{}] 检测到阻力位{}跌破：当前价格距离阻力位-{}%，有利于开空",
                               symbol, resistancePrice.toPlainString(), resistanceDistance.toPlainString());
                }
            }
        } catch (Exception e) {
            logger.debug("[{}] 阻力位检测异常，忽略: {}", symbol, e.getMessage());
        }
        
        // 3. 动态距离控制：如果有阻力位跌破，可以容忍更大距离
        BigDecimal maxDistanceThreshold = hasResistanceBreakthrough ? new BigDecimal("12.0") : new BigDecimal("6.0");
        
        if (currentPriceDistanceFromMA.compareTo(maxDistanceThreshold) > 0) {
            logger.info("[{}] 开空信号被拒绝：当前价格距离MA25过远-{}%>{}%，避免极端追空", 
                       symbol, currentPriceDistanceFromMA.toPlainString(), maxDistanceThreshold.toPlainString());
            return false;
        }
        
        BigDecimal distancePercent = firstKlineDistance.multiply(new BigDecimal("100"));
        String freshness = klinesAboveMA > 0 ? "新鲜跌破" : "延续下跌";
        String resistanceInfo = hasResistanceBreakthrough ? "有阻力位跌破支持" : "无明显阻力位参考";
        
        logger.info("[{}] 价格跌破MA25确认：连续{}根K线收盘价在MA25下方，第一根距离-{}%合理，当前距离MA25-{}%，{}-{}，最大容忍距离{}%", 
                   symbol, confirmationPeriods, distancePercent.toPlainString(), currentPriceDistanceFromMA.toPlainString(),
                   freshness, resistanceInfo, maxDistanceThreshold.toPlainString());
        return true;
    }

    public boolean isPriceConsecutivelyBelowMA(List<List<String>> klines, BigDecimal maValue, int confirmationPeriods) {
        // 🔥 优化：使用新的刚好跌破逻辑
        return isPriceJustBreakthroughBelowMA(klines, maValue, confirmationPeriods, "UNKNOWN");
    }

    /**
     * 检查开多信号 - 🔥 简化版：专注MA25核心逻辑，去除维加斯通道依赖
     * @param symbol 交易对
     * @return 是否发出开多信号
     */
    public boolean checkLongOpenSignal(String symbol) {
        // 🔥 简化：只获取MA25所需的K线数据（50根足够）
        List<List<String>> klines = binanceApiService.getKlines(symbol, "15m", tradingConfig.getMa25LookbackPeriods() + 10);
        if (klines.size() < tradingConfig.getMa25LookbackPeriods()) {
            logger.warn("[{}] K线数据不足，无法判断开多信号", symbol);
            return false;
        }

        // 提取收盘价
        List<BigDecimal> closePrices = klines.stream()
                .map(kline -> new BigDecimal(kline.get(4)))
                .collect(Collectors.toList());

        // 计算MA25
        List<BigDecimal> ma25List = technicalAnalyzer.calculateMA(closePrices, tradingConfig.getMa25Period());
        if (ma25List.size() < 2) {
            logger.warn("[{}] MA25数据不足，无法判断开多信号", symbol);
            return false;
        }

        BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
        BigDecimal currentMa25 = ma25List.get(ma25List.size() - 1);

        // 🔥 核心MA25逻辑（已经包含完整风控）
        // 1. 连续3根K线刚好突破MA25（包含距离控制、时机控制）
        boolean priceJustBreakthroughAboveMA25 = isPriceJustBreakthroughAboveMA(klines, currentMa25, tradingConfig.getMa25TurnPeriod(), symbol);

        // 2. 第一根K线必须在MA25下方（确保是真正的突破）
        boolean firstKlineBelowMa25 = false;
        int confirmationPeriods = tradingConfig.getMa25TurnPeriod();
        if (klines.size() > confirmationPeriods) {
            BigDecimal firstKlineClose = new BigDecimal(klines.get(klines.size() - 1 - confirmationPeriods).get(4));
            firstKlineBelowMa25 = firstKlineClose.compareTo(currentMa25) < 0;
        }

        // 3. 当前价格必须在MA25之上
        boolean priceAboveMa25 = currentPrice.compareTo(currentMa25) > 0;

        // 4. 最近3根K线中至少有2根阳线（灵活的形态确认）
        boolean recentKlinesBullish = checkRecentKlinesBullishFlexible(klines, 3, 2, symbol);
        
        // 5. 当前K线为阳线
        BigDecimal openPrice = new BigDecimal(klines.get(klines.size() - 1).get(1));
        BigDecimal closePrice = new BigDecimal(klines.get(klines.size() - 1).get(4));
        boolean isCurrentKlineBullish = closePrice.compareTo(openPrice) > 0;

        boolean ma25Signal = priceJustBreakthroughAboveMA25 && priceAboveMa25 && recentKlinesBullish && isCurrentKlineBullish && firstKlineBelowMa25;
        
        if (ma25Signal) {
            // 统计刚好突破的K线信息
            StringBuilder klineInfo = new StringBuilder();
            for (int i = 1; i <= tradingConfig.getMa25TurnPeriod(); i++) {
                BigDecimal klineClose = new BigDecimal(klines.get(klines.size() - i).get(4));
                klineInfo.append(String.format("第%d根:%.5f ", i, klineClose));
            }
            
            BigDecimal priceAboveMa25Distance = currentPrice.subtract(currentMa25).divide(currentMa25, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            BigDecimal klineChangePercent = closePrice.subtract(openPrice).divide(openPrice, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            
            logger.info("[{}] LONG_SIGNAL_CONFIRMED - MA25突破开多信号确认 | 连续{}根K线刚好突破MA25: {} | MA25: {} | 当前价: {} 高于MA25: +{}% | K线: 最近3根中≥2根阳线+当前阳线+{}%",
                    symbol, tradingConfig.getMa25TurnPeriod(), klineInfo.toString().trim(),
                    currentMa25.toPlainString(), currentPrice.toPlainString(), 
                    priceAboveMa25Distance.toPlainString(), klineChangePercent.toPlainString());
        } else {
            // 记录不满足条件的详细原因
            if (!priceJustBreakthroughAboveMA25) {
                logger.debug("[{}] 开多信号未满足：未连续{}根K线刚好突破MA25之上", symbol, tradingConfig.getMa25TurnPeriod());
            }
            if (!priceAboveMa25) {
                BigDecimal priceBelowMa25Distance = currentMa25.subtract(currentPrice).divide(currentMa25, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                logger.debug("[{}] 开多信号未满足：价格低于MA25 | 当前价: {} 低于MA25: -{}%", symbol, currentPrice.toPlainString(), priceBelowMa25Distance.toPlainString());
            }
            if (!recentKlinesBullish) {
                logger.debug("[{}] 开多信号未满足：最近3根K线中阳线数量不足2根", symbol);
            }
            if (!isCurrentKlineBullish) {
                logger.debug("[{}] 开多信号未满足：当前K线不是阳线", symbol);
            }
            if (!firstKlineBelowMa25) {
                logger.debug("[{}] 开多信号未满足：第一根K线未在MA25下方", symbol);
            }
        }
        return ma25Signal;
    }

    /**
     * 检查开空信号 - 🔥 简化版：专注MA25核心逻辑，去除维加斯通道依赖
     * @param symbol 交易对
     * @return 是否发出开空信号
     */
    public boolean checkShortOpenSignal(String symbol) {
        // 🔥 简化：只获取MA25所需的K线数据（50根足够）
        List<List<String>> klines = binanceApiService.getKlines(symbol, "15m", tradingConfig.getMa25LookbackPeriods() + 10);
        if (klines.size() < tradingConfig.getMa25LookbackPeriods()) {
            logger.warn("[{}] K线数据不足，无法判断开空信号", symbol);
            return false;
        }

        // 提取收盘价
        List<BigDecimal> closePrices = klines.stream()
                .map(kline -> new BigDecimal(kline.get(4)))
                .collect(Collectors.toList());

        // 计算MA25
        List<BigDecimal> ma25List = technicalAnalyzer.calculateMA(closePrices, tradingConfig.getMa25Period());
        if (ma25List.size() < 2) {
            logger.warn("[{}] MA25数据不足，无法判断开空信号", symbol);
            return false;
        }

        BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
        BigDecimal currentMa25 = ma25List.get(ma25List.size() - 1);

        // 🔥 核心MA25逻辑（已经包含完整风控）
        // 1. 连续3根K线刚好跌破MA25（包含距离控制、时机控制）
        boolean priceJustBreakthroughBelowMA25 = isPriceJustBreakthroughBelowMA(klines, currentMa25, tradingConfig.getMa25TurnPeriod(), symbol);

        // 2. 当前价格必须在MA25之下
        boolean priceBelowMa25 = currentPrice.compareTo(currentMa25) < 0;

        // 3. 最近3根K线中至少有2根阴线（灵活的形态确认）
        boolean recentKlinesBearish = checkRecentKlinesBearishFlexible(klines, 3, 2, symbol);
        
        // 4. 当前K线为阴线
        BigDecimal openPrice = new BigDecimal(klines.get(klines.size() - 1).get(1));
        BigDecimal closePrice = new BigDecimal(klines.get(klines.size() - 1).get(4));
        boolean isCurrentKlineBearish = closePrice.compareTo(openPrice) < 0;

        boolean ma25Signal = priceJustBreakthroughBelowMA25 && priceBelowMa25 && recentKlinesBearish && isCurrentKlineBearish;
        
        if (ma25Signal) {
            // 统计刚好跌破的K线信息
            StringBuilder klineInfo = new StringBuilder();
            for (int i = 1; i <= tradingConfig.getMa25TurnPeriod(); i++) {
                BigDecimal klineClose = new BigDecimal(klines.get(klines.size() - i).get(4));
                klineInfo.append(String.format("第%d根:%.5f ", i, klineClose));
            }
            
            BigDecimal priceBelowMa25Distance = currentMa25.subtract(currentPrice).divide(currentMa25, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            BigDecimal klineChangePercent = openPrice.subtract(closePrice).divide(openPrice, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            
            logger.info("[{}] SHORT_SIGNAL_CONFIRMED - MA25跌破开空信号确认 | 连续{}根K线刚好跌破MA25: {} | MA25: {} | 当前价: {} 低于MA25: -{}% | K线: 最近3根中≥2根阴线+当前阴线-{}%",
                    symbol, tradingConfig.getMa25TurnPeriod(), klineInfo.toString().trim(),
                    currentMa25.toPlainString(), currentPrice.toPlainString(), 
                    priceBelowMa25Distance.toPlainString(), klineChangePercent.toPlainString());
        } else {
            // 记录不满足条件的详细原因
            if (!priceJustBreakthroughBelowMA25) {
                logger.debug("[{}] 开空信号未满足：未连续{}根K线刚好跌破MA25之下", symbol, tradingConfig.getMa25TurnPeriod());
            }
            if (!priceBelowMa25) {
                BigDecimal priceAboveMa25Distance = currentPrice.subtract(currentMa25).divide(currentMa25, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                logger.debug("[{}] 开空信号未满足：价格高于MA25 | 当前价: {} 高于MA25: +{}%", symbol, currentPrice.toPlainString(), priceAboveMa25Distance.toPlainString());
            }
            if (!recentKlinesBearish) {
                logger.debug("[{}] 开空信号未满足：最近3根K线中阴线数量不足2根", symbol);
            }
            if (!isCurrentKlineBearish) {
                logger.debug("[{}] 开空信号未满足：当前K线不是阴线", symbol);
            }
        }
        return ma25Signal;
    }

    /**
     * 检查反转平仓信号
     * @param symbol 交易对
     * @param position 持仓信息
     * @return 是否发出反转平仓信号
     */
    public boolean checkReversalCloseSignal(String symbol, Position position) {
        // 获取15分钟K线数据
        List<List<String>> klines = binanceApiService.getKlines(symbol, "15m", tradingConfig.getMa25LookbackPeriods() + 5);
        if (klines.size() < tradingConfig.getMa25LookbackPeriods() + 5) {
            logger.warn("[{}] K线数据不足，无法判断反转平仓信号", symbol);
            return false;
        }

        // 提取收盘价
        List<BigDecimal> closePrices = klines.stream()
                .map(kline -> new BigDecimal(kline.get(4)))
                .collect(Collectors.toList());

        // 计算MA25
        List<BigDecimal> ma25List = technicalAnalyzer.calculateMA(closePrices, tradingConfig.getMa25Period());
        if (ma25List.size() < 2) {
            logger.warn("[{}] MA25数据不足，无法判断反转平仓信号", symbol);
            return false;
        }

        BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
        BigDecimal currentMa25 = ma25List.get(ma25List.size() - 1);

        // 判断是否有利润
        BigDecimal unrealizedPnl = position.calculatePnl(currentPrice);
        boolean hasProfit = unrealizedPnl.compareTo(BigDecimal.ZERO) > 0;

        if (!hasProfit) {
            return false; // 没有利润不考虑反转平仓
        }

        boolean reversalSignal = false;
        if ("LONG".equalsIgnoreCase(position.getPositionSide())) {
            // 多单：如果15分钟K柱有效跌破MA25日线
            reversalSignal = technicalAnalyzer.isPriceEffectivelyBrokenBelowMA(klines, currentMa25, tradingConfig.getConfirmationPeriods());
            if (reversalSignal) {
                logger.info("[{}] 多单检测到反转平仓信号：价格有效跌破MA25", symbol);
            }
        } else if ("SHORT".equalsIgnoreCase(position.getPositionSide())) {
            // 空单：如果15分钟K柱有效突破MA25日线
            reversalSignal = technicalAnalyzer.isPriceEffectivelyBrokenAboveMA(klines, currentMa25, tradingConfig.getConfirmationPeriods());
            if (reversalSignal) {
                logger.info("[{}] 空单检测到反转平仓信号：价格有效突破MA25", symbol);
            }
        }
        return reversalSignal;
    }

    /**
     * 检查多单止盈信号 - 🔥 优化版：只减仓一次，等待反转信号
     * @param symbol 交易对
     * @param position 持仓信息
     * @return 止盈类型
     */
    public String checkLongProfitSignal(String symbol, Position position) {
        // 🔥 简化：只获取MA25所需的K线数据（50根足够）
        List<List<String>> klines = binanceApiService.getKlines(symbol, "15m", tradingConfig.getMa25LookbackPeriods() + 10);
        if (klines.size() < tradingConfig.getMa25LookbackPeriods()) {
            logger.warn("[{}] K线数据不足，无法判断多单止盈信号", symbol);
            return null;
        }

        // 提取收盘价
        List<BigDecimal> closePrices = klines.stream()
                .map(kline -> new BigDecimal(kline.get(4)))
                .collect(Collectors.toList());

        // 计算MA25
        List<BigDecimal> ma25List = technicalAnalyzer.calculateMA(closePrices, tradingConfig.getMa25Period());
        if (ma25List.size() < 2) {
            logger.warn("[{}] MA25数据不足，无法判断多单止盈信号", symbol);
            return null;
        }

        BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
        BigDecimal currentMa25 = ma25List.get(ma25List.size() - 1);

        // 判断是否有利润
        BigDecimal unrealizedPnl = position.calculatePnl(currentPrice);
        boolean hasProfit = unrealizedPnl.compareTo(BigDecimal.ZERO) > 0;

        if (!hasProfit) {
            // 🔥 修复：亏损时不应该被平仓，也不应该止损，只加仓
            return null; // 亏损时不执行任何止盈止损逻辑
        }

        // 🔥 修复：使用扣除手续费后的净利润率
        BigDecimal profitPercentageAfterFees = calculateNetProfitPercentage(position, currentPrice);

        // 🔥 核心改进：检查是否已经减仓过，如果已减仓则只等待MA25反转信号
        if (position.isHasProfitReduced()) {
            logger.debug("[{}] 多单已进行过盈利减仓，只检查MA25反转信号", symbol);
            
            // 只检查MA25反转兜底（最后防线）
            boolean ma25Broken = technicalAnalyzer.isPriceEffectivelyBrokenBelowMA(klines, currentMa25, tradingConfig.getConfirmationPeriods());
            if (ma25Broken) {
                // 确保MA25反转时至少有0.5%净利润才平仓
                BigDecimal minimumProfitForMA25Reversal = new BigDecimal("0.005"); // 0.5%净利润门槛
                if (profitPercentageAfterFees.compareTo(minimumProfitForMA25Reversal) < 0) {
                    logger.info("[{}] LONG_MA25_BREAK_REJECTED - 多单MA25反转被拒绝 | MA25跌破确认但净利润{}%<0.5%，不足以覆盖手续费",
                               symbol, profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString());
                    return null;
                }
                
                BigDecimal priceBelowMa25Distance = currentMa25.subtract(currentPrice).divide(currentMa25, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                
                logger.info("[{}] LONG_MA25_BREAK_FINAL - 多单MA25反转最终平仓 | MA25: {} 跌破: -{}% | 净利润: {}%≥0.5% | 策略: 已减仓后的反转信号",
                           symbol, currentMa25.toPlainString(), priceBelowMa25Distance.toPlainString(),
                           profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString());
                return "FULL_CLOSE";
            }
            
            return null; // 已减仓，等待MA25反转
        }

        // 🔥 策略1：回本减仓策略（专门处理加仓后回本的情况）
        if (position.hasAddedPosition() && profitPercentageAfterFees.compareTo(new BigDecimal("0.004")) > 0) { // 盈利>0.4%才考虑回本减仓
            
                // 检查是否微盈利
            boolean isBreakevenProfit = profitPercentageAfterFees.compareTo(new BigDecimal("0.005")) > 0; // 盈利>0.5%
                
            if (isBreakevenProfit) { // 盈利>0.5%就执行减仓
                    // 🔥 修复：不在此处设置标记，等待实际减仓成功后再设置
                logger.info("[{}] LONG_BREAKEVEN_CLOSE_ADDED - 多单回本减仓 | 总盈利: {}% | 策略: 平掉加仓保本底仓",
                           symbol, profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString());
                    return "CLOSE_ADDED";
            }
        }

        // 🔥 策略2：高盈利一次性减仓（合并阻力位和高利润逻辑）
        if (profitPercentageAfterFees.compareTo(new BigDecimal("0.04")) > 0) { // 🔥 提高门槛：利润>4%才考虑减仓
            
            boolean shouldReduce = false;
            String reduceReason = "";
            
            // 检查阻力位条件 - 🔥 使用多时间周期
            try {
                BigDecimal resistancePrice = supportResistanceDetector.getMultiTimeframeResistance(symbol, currentPrice);

                if (resistancePrice != null && resistancePrice.compareTo(BigDecimal.valueOf(Double.MAX_VALUE)) < 0) {
                    // 判断是否接近阻力位（±3%范围内）
                    BigDecimal resistanceRange = resistancePrice.multiply(new BigDecimal("0.03"));
                    boolean nearResistance = currentPrice.subtract(resistancePrice).abs().compareTo(resistanceRange) <= 0;

                    if (nearResistance) {
                        shouldReduce = true;
                        reduceReason = "接近阻力位" + resistancePrice.toPlainString();
                                }
                }
            } catch (Exception e) {
                logger.warn("[{}] 阻力位检测异常: {}", symbol, e.getMessage());
        }

            // 如果没有阻力位信号，检查MA25调头
            if (!shouldReduce) {
            BigDecimal previousMa25 = ma25List.size() > 1 ? ma25List.get(ma25List.size() - 2) : currentMa25;
            boolean ma25TurningDown = currentMa25.compareTo(previousMa25) <= 0;
            
            if (ma25TurningDown) {
                    shouldReduce = true;
                    reduceReason = "MA25调头下降";
                }
            }
            
            if (shouldReduce) {
                // 🔥 修复：不在此处设置标记，等待实际减仓成功后再设置
                if (position.hasAddedPosition()) {
                    logger.info("[{}] LONG_PROFIT_CLOSE_ADDED - 多单盈利减仓（平加仓） | 利润: {}%≥4% | 原因: {} | 策略: 平掉加仓保留底仓",
                               symbol, profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString(), reduceReason);
                    return "CLOSE_ADDED";
                } else {
                    logger.info("[{}] LONG_PROFIT_REDUCE - 多单盈利减仓（50%） | 利润: {}%≥4% | 原因: {} | 策略: 50%减仓",
                               symbol, profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString(), reduceReason);
                    return "RESISTANCE_REDUCE";
                }
            }
        }

        // 🔥 策略3：MA25反转兜底（最后防线）
        boolean ma25Broken = technicalAnalyzer.isPriceEffectivelyBrokenBelowMA(klines, currentMa25, tradingConfig.getConfirmationPeriods());
        if (ma25Broken) {
            // 确保MA25反转时至少有0.5%净利润才平仓
            BigDecimal minimumProfitForMA25Reversal = new BigDecimal("0.005"); // 0.5%净利润门槛
            if (profitPercentageAfterFees.compareTo(minimumProfitForMA25Reversal) < 0) {
                logger.info("[{}] LONG_MA25_BREAK_REJECTED - 多单MA25反转被拒绝 | MA25跌破确认但净利润{}%<0.5%，不足以覆盖手续费",
                           symbol, profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString());
                return null;
            }
            
            BigDecimal priceBelowMa25Distance = currentMa25.subtract(currentPrice).divide(currentMa25, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            
            if (position.hasAddedPosition()) {
                // 🔥 修复：不在此处设置标记，等待实际减仓成功后再设置
                logger.info("[{}] LONG_MA25_BREAK_CLOSE_ADDED - 多单MA25反转兜底平加仓 | MA25: {} 跌破: -{}% | 净利润: {}%≥0.5% | 策略: 平掉加仓保留底仓",
                           symbol, currentMa25.toPlainString(), priceBelowMa25Distance.toPlainString(),
                           profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString());
                return "CLOSE_ADDED";
            } else if (shouldFullClose(symbol, position, "LONG")) {
                logger.info("[{}] LONG_MA25_BREAK_FULL - 多单MA25反转完全平仓 | MA25: {} 跌破: -{}% | 净利润: {}%≥0.5%",
                           symbol, currentMa25.toPlainString(), priceBelowMa25Distance.toPlainString(),
                           profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString());
                return "FULL_CLOSE";
            } else {
                // 🔥 修复：不在此处设置标记，等待实际减仓成功后再设置
                logger.info("[{}] LONG_MA25_BREAK_STANDARD - 多单MA25反转标准止盈 | MA25: {} 跌破: -{}% | 净利润: {}%≥0.5% | 策略: 50%减仓",
                           symbol, currentMa25.toPlainString(), priceBelowMa25Distance.toPlainString(),
                           profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString());
                return "RESISTANCE_REDUCE";
            }
        }

        return null; // 不满足止盈条件
    }

    /**
     * 检查空单止盈信号 - 🔥 优化版：只减仓一次，等待反转信号
     * @param symbol 交易对
     * @param position 持仓信息
     * @return 止盈类型
     */
    public String checkShortProfitSignal(String symbol, Position position) {
        // 🔥 简化：只获取MA25所需的K线数据（50根足够）
        List<List<String>> klines = binanceApiService.getKlines(symbol, "15m", tradingConfig.getMa25LookbackPeriods() + 10);
        if (klines.size() < tradingConfig.getMa25LookbackPeriods()) {
            logger.warn("[{}] K线数据不足，无法判断空单止盈信号", symbol);
            return null;
        }

        // 提取收盘价
        List<BigDecimal> closePrices = klines.stream()
                .map(kline -> new BigDecimal(kline.get(4)))
                .collect(Collectors.toList());

        // 计算MA25
        List<BigDecimal> ma25List = technicalAnalyzer.calculateMA(closePrices, tradingConfig.getMa25Period());
        if (ma25List.size() < 2) {
            logger.warn("[{}] MA25数据不足，无法判断空单止盈信号", symbol);
            return null;
        }

        BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
        BigDecimal currentMa25 = ma25List.get(ma25List.size() - 1);

        // 判断是否有利润
        BigDecimal unrealizedPnl = position.calculatePnl(currentPrice);
        boolean hasProfit = unrealizedPnl.compareTo(BigDecimal.ZERO) > 0;

        if (!hasProfit) {
            // 🔥 修复：亏损时不应该被平仓，也不应该止损，只加仓
            return null; // 亏损时不执行任何止盈止损逻辑
        }

        // 🔥 修复：使用扣除手续费后的净利润率
        BigDecimal profitPercentageAfterFees = calculateNetProfitPercentage(position, currentPrice);

        // 🔥 核心改进：检查是否已经减仓过，如果已减仓则只等待MA25反转信号
        if (position.isHasProfitReduced()) {
            logger.debug("[{}] 空单已进行过盈利减仓，只检查MA25反转信号", symbol);
            
            // 只检查MA25反转兜底（最后防线）
            boolean ma25Broken = technicalAnalyzer.isPriceEffectivelyBrokenAboveMA(klines, currentMa25, tradingConfig.getConfirmationPeriods());
            if (ma25Broken) {
                // 确保MA25反转时至少有0.5%净利润才平仓
                BigDecimal minimumProfitForMA25Reversal = new BigDecimal("0.005"); // 0.5%净利润门槛
                if (profitPercentageAfterFees.compareTo(minimumProfitForMA25Reversal) < 0) {
                    logger.info("[{}] SHORT_MA25_BREAK_REJECTED - 空单MA25反转被拒绝 | MA25突破确认但净利润{}%<0.5%，不足以覆盖手续费",
                               symbol, profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString());
                    return null;
                }
                
                BigDecimal priceAboveMa25Distance = currentPrice.subtract(currentMa25).divide(currentMa25, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                
                logger.info("[{}] SHORT_MA25_BREAK_FINAL - 空单MA25反转最终平仓 | MA25: {} 突破: +{}% | 净利润: {}%≥0.5% | 策略: 已减仓后的反转信号",
                           symbol, currentMa25.toPlainString(), priceAboveMa25Distance.toPlainString(),
                           profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString());
                return "FULL_CLOSE";
            }
            
            return null; // 已减仓，等待MA25反转
        }

        // 🔥 策略1：回本减仓策略（专门处理加仓后回本的情况）
        if (position.hasAddedPosition() && profitPercentageAfterFees.compareTo(new BigDecimal("0.004")) > 0) { // 盈利>0.4%才考虑回本减仓
            
                // 检查是否微盈利
            boolean isBreakevenProfit = profitPercentageAfterFees.compareTo(new BigDecimal("0.005")) > 0; // 盈利>0.5%
                
                // 🔥 修复：安全获取4小时支撑位作为参考，添加异常处理
                boolean hasSupportNearby = false;
                try {
                    BigDecimal supportPrice = supportResistanceDetector.getLatestSupport(symbol, "4h", currentPrice);
                    if (supportPrice != null && supportPrice.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal supportDistance = currentPrice.subtract(supportPrice).divide(currentPrice, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                        hasSupportNearby = supportDistance.abs().compareTo(new BigDecimal("5")) < 0; // 距离支撑位5%内
                    }
                } catch (Exception e) {
                    logger.debug("[{}] 空单回本减仓策略：支撑位检测异常，忽略支撑位因素: {}", symbol, e.getMessage());
                }
                
                if (isBreakevenProfit && (hasSupportNearby || profitPercentageAfterFees.compareTo(new BigDecimal("0.008")) > 0)) { // 微盈利0.8%以上或接近支撑位
                    // 🔥 修复：不在此处设置标记，等待实际减仓成功后再设置
                logger.info("[{}] SHORT_BREAKEVEN_CLOSE_ADDED - 空单回本减仓 | 总盈利: {}% | 策略: 平掉加仓保本底仓",
                           symbol, profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString());
                    return "CLOSE_ADDED";
            }
        }

        // 🔥 策略2：高盈利一次性减仓（合并支撑位和高利润逻辑）
        if (profitPercentageAfterFees.compareTo(new BigDecimal("0.04")) > 0) { // 🔥 提高门槛：利润>4%才考虑减仓
            
            boolean shouldReduce = false;
            String reduceReason = "";
            
            // 检查支撑位条件
            try {
                BigDecimal supportPrice = supportResistanceDetector.getLatestSupport(symbol, "4h", currentPrice);
                
                if (supportPrice != null && supportPrice.compareTo(BigDecimal.ZERO) > 0) {
                    // 判断是否接近支撑位（±3%范围内）
                    BigDecimal supportRange = supportPrice.multiply(new BigDecimal("0.03"));
                    boolean nearSupport = currentPrice.subtract(supportPrice).abs().compareTo(supportRange) <= 0;
                    
                    if (nearSupport) {
                        shouldReduce = true;
                        reduceReason = "接近支撑位" + supportPrice.toPlainString();
                                }
                }
            } catch (Exception e) {
                logger.warn("[{}] 支撑位检测异常: {}", symbol, e.getMessage());
        }

            // 如果没有支撑位信号，检查MA25调头
            if (!shouldReduce) {
            BigDecimal previousMa25 = ma25List.size() > 1 ? ma25List.get(ma25List.size() - 2) : currentMa25;
            boolean ma25TurningUp = currentMa25.compareTo(previousMa25) >= 0;
            
            if (ma25TurningUp) {
                    shouldReduce = true;
                    reduceReason = "MA25调头上升";
                }
            }
            
            if (shouldReduce) {
                // 🔥 修复：不在此处设置标记，等待实际减仓成功后再设置
                if (position.hasAddedPosition()) {
                    logger.info("[{}] SHORT_PROFIT_CLOSE_ADDED - 空单盈利减仓（平加仓） | 利润: {}%≥4% | 原因: {} | 策略: 平掉加仓保留底仓",
                               symbol, profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString(), reduceReason);
                    return "CLOSE_ADDED";
                } else {
                    logger.info("[{}] SHORT_PROFIT_REDUCE - 空单盈利减仓（50%） | 利润: {}%≥4% | 原因: {} | 策略: 50%减仓",
                               symbol, profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString(), reduceReason);
                    return "SUPPORT_REDUCE";
                }
            }
        }

        // 🔥 策略3：MA25反转兜底（最后防线）
        boolean ma25Broken = technicalAnalyzer.isPriceEffectivelyBrokenAboveMA(klines, currentMa25, tradingConfig.getConfirmationPeriods());
        if (ma25Broken) {
            // 确保MA25反转时至少有0.5%净利润才平仓
            BigDecimal minimumProfitForMA25Reversal = new BigDecimal("0.005"); // 0.5%净利润门槛
            if (profitPercentageAfterFees.compareTo(minimumProfitForMA25Reversal) < 0) {
                logger.info("[{}] SHORT_MA25_BREAK_REJECTED - 空单MA25反转被拒绝 | MA25突破确认但净利润{}%<0.5%，不足以覆盖手续费",
                           symbol, profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString());
                return null;
            }
            
            BigDecimal priceAboveMa25Distance = currentPrice.subtract(currentMa25).divide(currentMa25, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            
            if (position.hasAddedPosition()) {
                // 🔥 修复：不在此处设置标记，等待实际减仓成功后再设置
                logger.info("[{}] SHORT_MA25_BREAK_CLOSE_ADDED - 空单MA25反转兜底平加仓 | MA25: {} 突破: +{}% | 净利润: {}%≥0.5% | 策略: 平掉加仓保留底仓",
                           symbol, currentMa25.toPlainString(), priceAboveMa25Distance.toPlainString(),
                           profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString());
                return "CLOSE_ADDED";
            } else if (shouldFullClose(symbol, position, "SHORT")) {
                logger.info("[{}] SHORT_MA25_BREAK_FULL - 空单MA25反转完全平仓 | MA25: {} 突破: +{}% | 净利润: {}%≥0.5%",
                           symbol, currentMa25.toPlainString(), priceAboveMa25Distance.toPlainString(),
                           profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString());
                return "FULL_CLOSE";
            } else {
                // 🔥 修复：不在此处设置标记，等待实际减仓成功后再设置
                logger.info("[{}] SHORT_MA25_BREAK_STANDARD - 空单MA25反转标准止盈 | MA25: {} 突破: +{}% | 净利润: {}%≥0.5% | 策略: 50%减仓",
                           symbol, currentMa25.toPlainString(), priceAboveMa25Distance.toPlainString(),
                           profitPercentageAfterFees.multiply(new BigDecimal("100")).toPlainString());
                return "SUPPORT_REDUCE";
            }
        }

        return null; // 不满足止盈条件
    }

    /**
     * 检查是否可以在阻力位减仓（避免过度减仓）
     * @param symbol 交易对
     * @param position 持仓信息
     * @param positionSide 持仓方向
     * @return 是否可以减仓
     */
    private boolean canReduceAtResistance(String symbol, Position position, String positionSide) {
        // 1. 如果仓位已经很小，不再减仓（开仓30U，保护25U以下不减仓）
        BigDecimal currentPositionValue = position.getQuantity().multiply(tradingService.getCurrentPrice(symbol));
        if (currentPositionValue.compareTo(new BigDecimal("25")) <= 0) {
            logger.debug("[{}] {}仓位价值{}U已经很小，不再减仓", symbol, positionSide, currentPositionValue);
            return false;
        }

        // 2. 优化：限制减仓次数放宽到2次，给更多止盈机会
        Integer reduceCount = position.getReduceCount();
        if (reduceCount != null && reduceCount >= 2) {
            logger.info("[{}] {}持仓已减仓{}次，不再减仓，等待MA25信号", symbol, positionSide, reduceCount);
            return false;
        }

        // 3. 优化：减仓时间间隔缩短到3小时，更灵活
        if (position.getLastReduceTime() != null) {
            long hoursSinceLastReduce = java.time.Duration.between(position.getLastReduceTime(), java.time.LocalDateTime.now()).toHours();
            if (hoursSinceLastReduce < 3) {
                logger.info("[{}] {}持仓{}小时前刚减仓，间隔太短不再减仓", symbol, positionSide, hoursSinceLastReduce);
                return false;
            }
        }

        // 4. 优化：降低仓位价值门槛，适配底仓10U（设置为20U门槛）
        if (currentPositionValue.compareTo(new BigDecimal("20")) < 0) {
            logger.debug("[{}] {}仓位价值{}U较小，不需要减仓", symbol, positionSide, currentPositionValue);
            return false;
        }

        logger.info("[{}] {}持仓满足减仓条件：仓位价值{}U，减仓次数{}次", symbol, positionSide, currentPositionValue, reduceCount != null ? reduceCount : 0);
        return true;
    }

    /**
     * 检查是否可以在支撑位减仓（避免过度减仓）
     * @param symbol 交易对
     * @param position 持仓信息
     * @param positionSide 持仓方向
     * @return 是否可以减仓
     */
    private boolean canReduceAtSupport(String symbol, Position position, String positionSide) {
        // 1. 如果仓位已经很小，不再减仓（调整：底仓10U，设置为15U保护）
        BigDecimal currentPositionValue = position.getQuantity().multiply(tradingService.getCurrentPrice(symbol));
        if (currentPositionValue.compareTo(new BigDecimal("15")) <= 0) {
            logger.debug("[{}] {}仓位价值{}U已经很小，不再减仓", symbol, positionSide, currentPositionValue);
            return false;
        }

        // 2. 优化：限制减仓次数放宽到2次，给更多止盈机会
        Integer reduceCount = position.getReduceCount();
        if (reduceCount != null && reduceCount >= 2) {
            logger.info("[{}] {}持仓已减仓{}次，不再减仓，等待MA25信号", symbol, positionSide, reduceCount);
            return false;
        }

        // 3. 优化：减仓时间间隔缩短到3小时，更灵活
        if (position.getLastReduceTime() != null) {
            long hoursSinceLastReduce = java.time.Duration.between(position.getLastReduceTime(), java.time.LocalDateTime.now()).toHours();
            if (hoursSinceLastReduce < 3) {
                logger.info("[{}] {}持仓{}小时前刚减仓，间隔太短不再减仓", symbol, positionSide, hoursSinceLastReduce);
                return false;
            }
        }

        // 4. 优化：降低仓位价值门槛，适配底仓10U（设置为20U门槛）
        if (currentPositionValue.compareTo(new BigDecimal("20")) < 0) {
            logger.debug("[{}] {}仓位价值{}U较小，不需要减仓", symbol, positionSide, currentPositionValue);
            return false;
        }

        logger.info("[{}] {}持仓满足减仓条件：仓位价值{}U，减仓次数{}次", symbol, positionSide, currentPositionValue, reduceCount != null ? reduceCount : 0);
        return true;
    }

    /**
     * 判断是否应该完全平仓
     * @param symbol 交易对
     * @param position 持仓信息
     * @param positionSide 持仓方向
     * @return 是否应该完全平仓
     */
    private boolean shouldFullClose(String symbol, Position position, String positionSide) {
        // 1. 如果持仓时间过长，完全平仓（现在是7天，基本不会触发）
        if (position.getCreatedAt() != null) {
            long holdingHours = java.time.Duration.between(position.getCreatedAt(), java.time.LocalDateTime.now()).toHours();
            if (holdingHours > tradingConfig.getRisk().getMaxHoldingHours()) {
                logger.info("[{}] {}持仓时间{}小时，超过最大值{}小时，完全平仓", symbol, positionSide, 
                           holdingHours, tradingConfig.getRisk().getMaxHoldingHours());
                return true;
            }
        }

        // 2. 如果没有加仓部分，且利润很小（但必须是盈利状态），完全平仓避免手续费侵蚀
        if (!position.hasAddedPosition()) {
            BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
            BigDecimal unrealizedPnl = position.calculatePnl(currentPrice);
            // 🔥 修复：使用净利润率而非毛利润率
            BigDecimal netProfitPercentage = calculateNetProfitPercentage(position, currentPrice);
            
            // 修复：只有在盈利且利润很小的情况下才考虑平仓，亏损时不执行此逻辑
            if (unrealizedPnl.compareTo(BigDecimal.ZERO) > 0 && 
                netProfitPercentage.compareTo(tradingConfig.getRisk().getMinProfitPercentage()) < 0) {
                logger.info("[{}] {}持仓无加仓部分且净利润{}%小于要求{}%，完全平仓避免手续费侵蚀", symbol, positionSide, 
                           netProfitPercentage.multiply(new BigDecimal("100")), tradingConfig.getRisk().getMinProfitPercentage());
                return true;
            }
        }

        // 3. 如果亏损过大，达到止损条件，完全平仓
        BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
        BigDecimal unrealizedPnl = position.calculatePnl(currentPrice);
        if (unrealizedPnl.compareTo(tradingConfig.getRisk().getMaxLossUsdt().negate()) <= 0) {
            logger.info("[{}] {}持仓亏损{}U达到止损线{}U，完全平仓", symbol, positionSide, 
                       unrealizedPnl.abs(), tradingConfig.getRisk().getMaxLossUsdt());
            return true;
        }

        return false; // 其他情况优先使用分层平仓
    }

    /**
     * 🔥 新增：检查最近N根K线是否都为阳线
     * @param klines K线数据
     * @param count 检查的K线数量
     * @param symbol 交易对符号（用于日志）
     * @return 是否都为阳线
     */
    private boolean checkRecentKlinesBullish(List<List<String>> klines, int count, String symbol) {
        if (klines.size() < count) {
            return false;
        }
        
        for (int i = 0; i < count; i++) {
            List<String> kline = klines.get(klines.size() - 1 - i);
            BigDecimal open = new BigDecimal(kline.get(1));
            BigDecimal close = new BigDecimal(kline.get(4));
            
            if (close.compareTo(open) <= 0) {
                return false; // 发现阴线或十字星，返回false
            }
        }
        
        logger.debug("[{}] 最近{}根K线都为阳线", symbol, count);
        return true;
    }
    
    /**
     * 🔥 新增：检查最近N根K线是否都为阴线
     * @param klines K线数据
     * @param count 检查的K线数量
     * @param symbol 交易对符号（用于日志）
     * @return 是否都为阴线
     */
    private boolean checkRecentKlinesBearish(List<List<String>> klines, int count, String symbol) {
        if (klines.size() < count) {
            return false;
        }
        
        for (int i = 0; i < count; i++) {
            List<String> kline = klines.get(klines.size() - 1 - i);
            BigDecimal open = new BigDecimal(kline.get(1));
            BigDecimal close = new BigDecimal(kline.get(4));
            
            if (close.compareTo(open) >= 0) {
                return false; // 发现阳线或十字星，返回false
            }
        }
        
        logger.debug("[{}] 最近{}根K线都为阴线", symbol, count);
        return true;
    }

    /**
     * 检查是否可以开仓（增加冷却期）
     * @param symbol 交易对
     * @param positionSide 持仓方向
     * @return 是否可以开仓
     */
    public boolean canOpenPosition(String symbol, String positionSide) {
        // 检查是否有最近平仓的记录
        List<Position> recentClosedPositions = tradingService.getRecentClosedPositions(symbol, positionSide, 2); // 最近2小时
        
        if (!recentClosedPositions.isEmpty()) {
            Position lastClosed = recentClosedPositions.get(0);
            if (lastClosed.getCloseTime() != null) {
                long minutesSinceClose = java.time.Duration.between(lastClosed.getCloseTime(), java.time.LocalDateTime.now()).toMinutes();
                if (minutesSinceClose < tradingConfig.getRisk().getCooldownMinutes()) {
                    logger.info("[{}] {}方向{}分钟前刚平仓，冷却期{}分钟内不开仓", symbol, positionSide, 
                               minutesSinceClose, tradingConfig.getRisk().getCooldownMinutes());
                    return false;
                }
            }
        }
        
        return true;
    }

    /**
     * 执行交易信号分析（手动触发入口）
     * @param symbol 交易对
     */
    public void executeTradingSignalAnalysis(String symbol) {
        logger.info("[{}] 开始执行MA25交易信号分析", symbol);
        
        try {
            // 1. 检查开多信号
            boolean longSignal = checkLongOpenSignal(symbol);
            if (longSignal) {
                logger.info("[{}] 检测到开多信号", symbol);
            } else {
                logger.debug("[{}] 暂无开多信号", symbol);
            }
            
            // 2. 检查开空信号
            boolean shortSignal = checkShortOpenSignal(symbol);
            if (shortSignal) {
                logger.info("[{}] 检测到开空信号", symbol);
            } else {
                logger.debug("[{}] 暂无开空信号", symbol);
            }
            
            // 3. 检查现有持仓的止盈信号
            List<Position> activePositions = tradingService.getActivePositionsBySymbol(symbol);
            for (Position position : activePositions) {
                if ("LONG".equalsIgnoreCase(position.getPositionSide())) {
                    String profitSignal = checkLongProfitSignal(symbol, position);
                    if (profitSignal != null) {
                        logger.info("[{}] 多单检测到止盈信号：{}", symbol, profitSignal);
                    }
                } else if ("SHORT".equalsIgnoreCase(position.getPositionSide())) {
                    String profitSignal = checkShortProfitSignal(symbol, position);
                    if (profitSignal != null) {
                        logger.info("[{}] 空单检测到止盈信号：{}", symbol, profitSignal);
                    }
                }
            }
            
            logger.info("[{}] MA25交易信号分析完成", symbol);
            
        } catch (Exception e) {
            logger.error("[{}] 执行交易信号分析异常: {}", symbol, e.getMessage());
        }
    }

    /**
     * 检查反转平仓信号（重载方法，用于Controller调用）
     * @param symbol 交易对
     */
    public void checkReversalCloseSignal(String symbol) {
        logger.info("[{}] 开始检查反转平仓信号", symbol);
        
        try {
            List<Position> activePositions = tradingService.getActivePositionsBySymbol(symbol);
            
            if (activePositions.isEmpty()) {
                logger.info("[{}] 没有活跃持仓，无需检查反转平仓信号", symbol);
                return;
            }
            
            for (Position position : activePositions) {
                boolean reversalSignal = checkReversalCloseSignal(symbol, position);
                if (reversalSignal) {
                    logger.info("[{}] {}持仓检测到反转平仓信号", symbol, position.getPositionSide());
                } else {
                    logger.debug("[{}] {}持仓暂无反转平仓信号", symbol, position.getPositionSide());
                }
            }
            
        } catch (Exception e) {
            logger.error("[{}] 检查反转平仓信号异常: {}", symbol, e.getMessage());
        }
    }

    /**
     * 检查阻力位过滤条件（开仓前的风险控制）
     * @param symbol 交易对
     * @param currentPrice 当前价格
     * @param direction 方向（LONG/SHORT）
     * @return 是否通过阻力位过滤
     */
    private boolean checkResistanceFilter(String symbol, BigDecimal currentPrice, String direction) {
        try {
            // 获取4小时支撑阻力位
            SupportResistanceDetector.SupportResistanceResult result = 
                supportResistanceDetector.detectLevels(symbol, "4h");
            
            if (result == null) {
                logger.debug("[{}] 无法获取支撑阻力位，默认通过过滤", symbol);
                return true; // 无法获取数据时默认通过
            }
            
            if ("LONG".equals(direction)) {
                // 开多：检查是否接近阻力位
                BigDecimal nearestResistance = result.getNearestResistanceLevel(currentPrice);
                if (nearestResistance.compareTo(BigDecimal.valueOf(Double.MAX_VALUE)) < 0) {
                    // 计算距离阻力位的百分比
                    BigDecimal distanceToResistance = nearestResistance.subtract(currentPrice)
                        .divide(currentPrice, 4, RoundingMode.HALF_UP);
                    
                    // 如果距离阻力位小于1%，拒绝开多
                    if (distanceToResistance.compareTo(new BigDecimal("0.01")) < 0) {
                        BigDecimal distancePercent = distanceToResistance.multiply(new BigDecimal("100"));
                        logger.info("[{}] 阻力位过滤拒绝开多 | 阻力位: {} 距离: +{}% < 1% | 策略: 避免阻力位附近开多", 
                                   symbol, nearestResistance.toPlainString(), distancePercent.toPlainString());
                        return false;
                    }
                }
                return true;
                
            } else if ("SHORT".equals(direction)) {
                // 开空：检查是否接近支撑位
                BigDecimal nearestSupport = result.getNearestSupportLevel(currentPrice);
                if (nearestSupport.compareTo(BigDecimal.ZERO) > 0) {
                    // 计算距离支撑位的百分比
                    BigDecimal distanceToSupport = currentPrice.subtract(nearestSupport)
                        .divide(currentPrice, 4, RoundingMode.HALF_UP);
                    
                    // 如果距离支撑位小于1%，拒绝开空
                    if (distanceToSupport.compareTo(new BigDecimal("0.01")) < 0) {
                        BigDecimal distancePercent = distanceToSupport.multiply(new BigDecimal("100"));
                        logger.info("[{}] 支撑位过滤拒绝开空 | 支撑位: {} 距离: -{}% < 1% | 策略: 避免支撑位附近开空", 
                                   symbol, nearestSupport.toPlainString(), distancePercent.toPlainString());
                        return false;
                    }
                }
                return true;
            }
            
            return true;
            
        } catch (Exception e) {
            logger.error("[{}] 检查阻力位过滤异常: {}", symbol, e.getMessage());
            return true; // 出错时默认通过过滤
        }
    }

    /**
     * 🔥 优化：检查最近N根K线中阴线的比例
     * @param klines K线数据
     * @param checkCount 检查的K线数量
     * @param minBearishCount 最少阴线数量
     * @param symbol 交易对符号（用于日志）
     * @return 是否满足阴线要求
     */
    private boolean checkRecentKlinesBearishFlexible(List<List<String>> klines, int checkCount, int minBearishCount, String symbol) {
        if (klines.size() < checkCount) {
            return false;
        }
        
        int bearishCount = 0;
        for (int i = 0; i < checkCount; i++) {
            List<String> kline = klines.get(klines.size() - 1 - i);
            BigDecimal open = new BigDecimal(kline.get(1));
            BigDecimal close = new BigDecimal(kline.get(4));
            
            if (close.compareTo(open) < 0) {
                bearishCount++; // 发现阴线
            }
        }
        
        boolean result = bearishCount >= minBearishCount;
        logger.debug("[{}] 最近{}根K线中有{}根阴线，要求≥{}根，结果: {}", symbol, checkCount, bearishCount, minBearishCount, result ? "满足" : "不满足");
        return result;
    }
    
    /**
     * 🔥 优化：检查最近N根K线中阳线的比例
     * @param klines K线数据
     * @param checkCount 检查的K线数量
     * @param minBullishCount 最少阳线数量
     * @param symbol 交易对符号（用于日志）
     * @return 是否满足阳线要求
     */
    private boolean checkRecentKlinesBullishFlexible(List<List<String>> klines, int checkCount, int minBullishCount, String symbol) {
        if (klines.size() < checkCount) {
            return false;
        }
        
        int bullishCount = 0;
        for (int i = 0; i < checkCount; i++) {
            List<String> kline = klines.get(klines.size() - 1 - i);
            BigDecimal open = new BigDecimal(kline.get(1));
            BigDecimal close = new BigDecimal(kline.get(4));
            
            if (close.compareTo(open) > 0) {
                bullishCount++; // 发现阳线
            }
        }
        
        boolean result = bullishCount >= minBullishCount;
        logger.debug("[{}] 最近{}根K线中有{}根阳线，要求≥{}根，结果: {}", symbol, checkCount, bullishCount, minBullishCount, result ? "满足" : "不满足");
        return result;
    }
}

