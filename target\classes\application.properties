# 应用基础配置
spring.application.name=ma25-trading-bot
server.port=8089

spring.datasource.url=jdbc:h2:mem:ma25_trading_bot_db;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=ma25_user
spring.datasource.password=ma25_pass

spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

spring.h2.console.enabled=true
spring.h2.console.path=/ma25-h2-console

spring.task.scheduling.pool.size=5

trading.base-url=https://fapi.binance.com
trading.api-key=hCyv6Jv7AzyPaxFJ9d4uPPASUT7PkeqKwNWkN5KGkWqEiD2dmwtbhiajtCnICxTD
trading.secret-key=6tkZqfEEvtCRZUbxbSPIqPj1ReapuUoUUqkHplwqSEun4zZblBJTqO0SaDhdlR5r

trading.symbols=APEUSDT,DOTUSDT,ENAUSDT,WIFUSDT,PENGUUSDT,BABYUSDT,1000PEPEUSDT,XRPUSDT,SUIUSDT,DOGEUSDT,LDOUSDT,TONUSDT,BNBUSDT,UNIUSDT,ADAUSDT

# 生生不息配置 - 固定开仓金额控制风险
trading.dynamic-position.base-position-usdt=30.0
trading.dynamic-position.max-position-usdt=30.0
trading.dynamic-position.profit-reinvest-rate=0.0
trading.dynamic-position.consecutive-win-bonus=999
trading.dynamic-position.win-bonus-multiplier=1.0
#trading.api-key=f7bca4fd6cf97f5ed608cf53761fb87f003bceff767088c61cc60a48a05124b3
#trading.secret-key=f6874ee798ddfa9f34902cf42769ca3c400d2bf45df04ad7d75b25857b319f0c
#trading.base-url=https://testnet.binancefuture.com
trading.testnet=true
trading.strategy-name=MA25Strategy
trading.leverage=20


trading.network.offline-mode=false
trading.network.connection-timeout=60000
trading.network.read-timeout=60000
trading.network.retry-count=3
trading.network.retry-delay=2000


trading.strategy.timeframe=15m
trading.strategy.ma-period=25
trading.strategy.confirmation-periods=2
trading.strategy.initial-position-usdt=1.5
trading.strategy.ma25=25
trading.strategy.ma25-lookback-periods=50
trading.strategy.ma25-turn-period=3

trading.risk.max-loss-usdt=100
trading.risk.base-position-usdt=0.5
trading.risk.max-position-multiplier=3
trading.risk.profit-take-threshold=2
trading.risk.max-add-count=3
trading.risk.max-holding-hours=8760
trading.risk.min-profit-percentage=2.0
trading.risk.cooldown-minutes=30
trading.risk.max-reduce-count=1
trading.risk.reduce-interval-hours=6
trading.risk.min-reduce-position-usdt=5
# 🔥 动态止损配置已禁用，改为固定总亏损限制
# trading.risk.enable-dynamic-stop-loss=false
# trading.risk.dynamic-stop-loss-percentage=0.15
# trading.risk.max-dynamic-stop-loss-usdt=50.0

# 🔥 新增：维加斯通道追高追低保护配置（增强开仓条件）
trading.risk.enable-vegas-anti-chasing=true
trading.risk.short-term-risk-threshold=0.05
trading.risk.medium-term-risk-threshold=0.12
trading.risk.min-dynamic-threshold=0.003
trading.risk.max-dynamic-threshold=0.025

# 🔥 新增：维加斯通道配置
trading.vegas-channel.enabled=true
trading.vegas-channel.fast-period=144
trading.vegas-channel.slow-period=169
trading.vegas-channel.trend-filter=true

trading.add-position.min-add-position-distance=2.0
trading.add-position.limit-order-timeout-minutes=5
trading.add-position.add-position-usdt=1.5
trading.add-position.min-history-distance=2.0
trading.add-position.support-resistance-buffer=1.0
trading.add-position.check-interval-minutes=5

# 🔥 优化：支撑阻力位配置 - 降低检测门槛，增加成交机会
trading.support-resistance.enabled=true
trading.support-resistance.timeframe=4h
trading.support-resistance.pivot-lookback=15
trading.support-resistance.pivot-threshold=0.10
trading.support-resistance.volume-profile-bins=20
trading.support-resistance.horizontal-lookback=12
trading.support-resistance.horizontal-threshold=0.15
trading.support-resistance.merge-threshold=0.015
trading.support-resistance.dynamic-range-percentage=0.015

logging.level.com.trading.bot=DEBUG
logging.level.com.binance=WARN
logging.level.org.springframework=WARN
logging.level.org.hibernate=WARN
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.file.name=logs/ma25-trading-bot.log

spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=1000,expireAfterWrite=5m
spring.cache.cache-names=ma25-klines,ma25-prices,ma25-support-resistance

management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized

# 🔥 新增：反转站稳市价加仓配置
trading.add-position.enable-reversal-market-add=true
trading.add-position.reversal-loss-threshold=5.0
trading.add-position.reversal-ma-periods=3

