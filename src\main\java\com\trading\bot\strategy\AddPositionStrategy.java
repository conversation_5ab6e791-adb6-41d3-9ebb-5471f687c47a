package com.trading.bot.strategy;

import com.trading.bot.analysis.SupportResistanceDetector;
import com.trading.bot.config.TradingConfig;
import com.trading.bot.entity.Position;
import com.trading.bot.entity.TradeRecord;
import com.trading.bot.service.BinanceApiService;
import com.trading.bot.service.TradingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 加仓策略
 * 赚钱的时候不加仓：亏钱了并且K线再次调头平空或者平多翻倍加仓，如果加仓至少比开仓距离要间隔2个点，
 * 并且结合4小时支撑阻力位来挂单加仓，每次加仓间隔至少2个点
 */
@Component
public class AddPositionStrategy {

    private static final Logger logger = LoggerFactory.getLogger(AddPositionStrategy.class);

    @Autowired
    private TradingService tradingService;

    @Autowired
    private BinanceApiService binanceApiService;

    @Autowired
    private SupportResistanceDetector supportResistanceDetector;

    @Autowired
    private TradingConfig tradingConfig;

    @Autowired
    private com.trading.bot.analysis.TechnicalAnalyzer technicalAnalyzer;

    /**
     * 🔥 新增：检查反转站稳市价加仓信号
     * @param symbol 交易对
     * @param position 持仓信息
     * @return 是否应该市价加仓
     */
    public boolean shouldMarketAddOnReversal(String symbol, Position position) {
        try {
            // 🔥 配置检查：是否启用反转站稳市价加仓
            if (!tradingConfig.getAddPosition().isEnableReversalMarketAdd()) {
                return false;
            }
            
            BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
            if (currentPrice == null || currentPrice.compareTo(BigDecimal.ZERO) <= 0) {
                return false;
            }
            
            // 1. 检查亏损是否达到配置的门槛
            BigDecimal unrealizedPnl = position.calculatePnl(currentPrice);
            if (unrealizedPnl.compareTo(BigDecimal.ZERO) >= 0) {
                logger.debug("[{}] {}持仓当前盈利，不符合反转加仓条件", symbol, position.getPositionSide());
                return false;
            }
            
            BigDecimal lossThreshold = tradingConfig.getAddPosition().getReversalLossThreshold();
            BigDecimal lossPercentage = unrealizedPnl.abs().divide(position.getTotalAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            
            if (lossPercentage.compareTo(lossThreshold) < 0) {
                logger.debug("[{}] {}持仓亏损{}%未达到{}%门槛", symbol, position.getPositionSide(), 
                           lossPercentage.toPlainString(), lossThreshold.toPlainString());
                return false;
            }
            
            // 2. 检查MA25反转站稳确认
            List<List<String>> klines = binanceApiService.getKlines(symbol, "15m", 60);
            if (klines.size() < 30) {
                logger.debug("[{}] K线数据不足，无法判断MA25反转", symbol);
                return false;
            }
            
            boolean reversalConfirmed = false;
            if ("LONG".equals(position.getPositionSide())) {
                // 多单：MA25调头上升 + 价格站稳MA25上方 + 当前K线阳线
                boolean ma25TurningUp = isMa25TurningUp(klines, 25);
                boolean priceAboveMA25 = isPriceAboveMA25(klines, symbol);
                boolean currentKLineBullish = isCurrentKLineBullish(klines);
                
                reversalConfirmed = ma25TurningUp && priceAboveMA25 && currentKLineBullish;
                
                if (reversalConfirmed) {
                    logger.info("[{}] 多单反转站稳确认：MA25调头上升 + 价格站稳MA25上方 + 当前K线阳线", symbol);
                }
            } else if ("SHORT".equals(position.getPositionSide())) {
                // 空单：MA25调头下降 + 价格跌破MA25下方 + 当前K线阴线
                boolean ma25TurningDown = isMa25TurningDown(klines, 25);
                boolean priceBelowMA25 = isPriceBelowMA25(klines, symbol);
                boolean currentKLineBearish = isCurrentKLineBearish(klines);
                
                reversalConfirmed = ma25TurningDown && priceBelowMA25 && currentKLineBearish;
                
                if (reversalConfirmed) {
                    logger.info("[{}] 空单反转站稳确认：MA25调头下降 + 价格跌破MA25下方 + 当前K线阴线", symbol);
                }
            }
            
            if (reversalConfirmed) {
                // 🔥 新增：详细计算过程日志
                logger.info("[{}] {} REVERSAL_MARKET_ADD_SIGNAL - {}反转站稳市价加仓信号:", symbol, position.getPositionSide(), 
                           "LONG".equals(position.getPositionSide()) ? "多单" : "空单");
                logger.info("[{}]    ├─ 亏损检查: {}% ≥ {}% ✓", symbol, lossPercentage.toPlainString(), lossThreshold.toPlainString());
                logger.info("[{}]    ├─ MA25反转: {} ✓", symbol, "LONG".equals(position.getPositionSide()) ? "调头上升" : "调头下降");
                logger.info("[{}]    ├─ 价格位置: {} ✓", symbol, "LONG".equals(position.getPositionSide()) ? "站稳MA25上方" : "跌破MA25下方");
                logger.info("[{}]    └─ K线确认: {} ✓", symbol, "LONG".equals(position.getPositionSide()) ? "当前阳线" : "当前阴线");
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            logger.error("[{}] 检查反转站稳市价加仓信号异常: {}", symbol, e.getMessage());
            return false;
        }
    }
    
    /**
     * 🔥 新增：检查MA25是否调头上升
     */
    public boolean isMa25TurningUp(List<List<String>> klines, int maPeriod) {
        try {
            List<BigDecimal> closePrices = klines.stream()
                    .map(kline -> new BigDecimal(kline.get(4)))
                    .collect(Collectors.toList());
            List<BigDecimal> ma25List = technicalAnalyzer.calculateMA(closePrices, maPeriod);
            
            if (ma25List.size() < 3) {
                return false;
            }
            
            BigDecimal currentMa25 = ma25List.get(ma25List.size() - 1);
            BigDecimal previousMa25 = ma25List.get(ma25List.size() - 2);
            BigDecimal prevPreviousMa25 = ma25List.get(ma25List.size() - 3);
            
            // MA25连续2期上升
            return currentMa25.compareTo(previousMa25) > 0 && previousMa25.compareTo(prevPreviousMa25) > 0;
        } catch (Exception e) {
            logger.error("检查MA25调头上升异常: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 🔥 新增：检查MA25是否调头下降
     */
    public boolean isMa25TurningDown(List<List<String>> klines, int maPeriod) {
        try {
            List<BigDecimal> closePrices = klines.stream()
                    .map(kline -> new BigDecimal(kline.get(4)))
                    .collect(Collectors.toList());
            List<BigDecimal> ma25List = technicalAnalyzer.calculateMA(closePrices, maPeriod);
            
            if (ma25List.size() < 3) {
                return false;
            }
            
            BigDecimal currentMa25 = ma25List.get(ma25List.size() - 1);
            BigDecimal previousMa25 = ma25List.get(ma25List.size() - 2);
            BigDecimal prevPreviousMa25 = ma25List.get(ma25List.size() - 3);
            
            // MA25连续2期下降
            return currentMa25.compareTo(previousMa25) < 0 && previousMa25.compareTo(prevPreviousMa25) < 0;
        } catch (Exception e) {
            logger.error("检查MA25调头下降异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 🔥 新增：检查当前K线是否为阳线
     */
    private boolean isCurrentKLineBullish(List<List<String>> klines) {
        if (klines.isEmpty()) {
            return false;
        }
        List<String> currentKline = klines.get(klines.size() - 1);
        BigDecimal open = new BigDecimal(currentKline.get(1));
        BigDecimal close = new BigDecimal(currentKline.get(4));
        return close.compareTo(open) > 0;
    }
    
    /**
     * 🔥 新增：检查当前K线是否为阴线
     */
    private boolean isCurrentKLineBearish(List<List<String>> klines) {
        if (klines.isEmpty()) {
            return false;
        }
        List<String> currentKline = klines.get(klines.size() - 1);
        BigDecimal open = new BigDecimal(currentKline.get(1));
        BigDecimal close = new BigDecimal(currentKline.get(4));
        return close.compareTo(open) < 0;
    }

    /**
     * 🔥 新增：检查价格是否连续N个周期站稳MA25上方
     */
    private boolean isPriceAboveMA25(List<List<String>> klines, String symbol) {
        try {
            List<BigDecimal> closePrices = klines.stream()
                    .map(kline -> new BigDecimal(kline.get(4)))
                    .collect(Collectors.toList());
            List<BigDecimal> ma25List = technicalAnalyzer.calculateMA(closePrices, 25);

            int confirmationPeriods = tradingConfig.getAddPosition().getReversalMaPeriods(); // 使用配置的周期数

            if (ma25List.size() < confirmationPeriods || klines.size() < confirmationPeriods) {
                logger.warn("[{}] MA25数据不足，无法判断价格连续{}个周期站稳MA25上方", symbol, confirmationPeriods);
                return false;
            }

            BigDecimal currentMa25 = ma25List.get(ma25List.size() - 1);

            // 🔥 检查最近N个周期的收盘价是否都在MA25上方
            for (int i = 0; i < confirmationPeriods; i++) {
                BigDecimal klineClose = new BigDecimal(klines.get(klines.size() - 1 - i).get(4)); // 收盘价
                if (klineClose.compareTo(currentMa25) <= 0) {
                    logger.debug("[{}] 第{}根K线收盘价{}未站稳MA25上方{}，反转确认失败",
                               symbol, i + 1, klineClose.toPlainString(), currentMa25.toPlainString());
                    return false; // 任何一根K线收盘价不在MA25上方，就不算站稳
                }
            }

            logger.debug("[{}] 连续{}根K线收盘价都站稳MA25上方，反转确认成功", symbol, confirmationPeriods);
            return true;
        } catch (Exception e) {
            logger.error("[{}] 检查价格站稳MA25上方异常: {}", symbol, e.getMessage());
            return false;
        }
    }

    /**
     * 🔥 新增：检查价格是否连续N个周期跌破MA25下方
     */
    private boolean isPriceBelowMA25(List<List<String>> klines, String symbol) {
        try {
            List<BigDecimal> closePrices = klines.stream()
                    .map(kline -> new BigDecimal(kline.get(4)))
                    .collect(Collectors.toList());
            List<BigDecimal> ma25List = technicalAnalyzer.calculateMA(closePrices, 25);

            int confirmationPeriods = tradingConfig.getAddPosition().getReversalMaPeriods(); // 使用配置的周期数

            if (ma25List.size() < confirmationPeriods || klines.size() < confirmationPeriods) {
                logger.warn("[{}] MA25数据不足，无法判断价格连续{}个周期跌破MA25下方", symbol, confirmationPeriods);
                return false;
            }

            BigDecimal currentMa25 = ma25List.get(ma25List.size() - 1);

            // 🔥 检查最近N个周期的收盘价是否都在MA25下方
            for (int i = 0; i < confirmationPeriods; i++) {
                BigDecimal klineClose = new BigDecimal(klines.get(klines.size() - 1 - i).get(4)); // 收盘价
                if (klineClose.compareTo(currentMa25) >= 0) {
                    logger.debug("[{}] 第{}根K线收盘价{}未跌破MA25下方{}，反转确认失败",
                               symbol, i + 1, klineClose.toPlainString(), currentMa25.toPlainString());
                    return false; // 任何一根K线收盘价不在MA25下方，就不算跌破
                }
            }

            logger.debug("[{}] 连续{}根K线收盘价都跌破MA25下方，反转确认成功", symbol, confirmationPeriods);
            return true;
        } catch (Exception e) {
            logger.error("[{}] 检查价格跌破MA25下方异常: {}", symbol, e.getMessage());
            return false;
        }
    }

    /**
     * 检查加仓信号
     * @param symbol 交易对
     * @return 是否有加仓信号
     */
    public boolean checkAddPositionSignal(String symbol) {
        try {
            Optional<Position> longPosition = tradingService.getActivePosition(symbol, "LONG");
            Optional<Position> shortPosition = tradingService.getActivePosition(symbol, "SHORT");
            
            // 🔥 优化：只检查反转站稳市价加仓信号
            if (longPosition.isPresent()) {
                boolean shouldMarketAdd = shouldMarketAddOnReversal(symbol, longPosition.get());
                if (shouldMarketAdd) {
                    return true; // 多单市价加仓
                }
            }
            
            if (shortPosition.isPresent()) {
                boolean shouldMarketAdd = shouldMarketAddOnReversal(symbol, shortPosition.get());
                if (shouldMarketAdd) {
                    return true; // 空单市价加仓
                }
            }
            
            return false;
            
        } catch (Exception e) {
            logger.error("[{}] 检查加仓信号异常: {}", symbol, e.getMessage());
            return false;
        }
    }
}

