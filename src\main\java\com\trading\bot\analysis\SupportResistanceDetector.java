package com.trading.bot.analysis;

import com.trading.bot.config.SupportResistanceProperties;
import com.trading.bot.service.BinanceApiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 支撑阻力位检测器 - 优化版本，解决密集检测问题
 * 基于参考项目优化，增加缓存机制和更严格的过滤条件
 */
@Component
public class SupportResistanceDetector {
    private static final Logger logger = LoggerFactory.getLogger(SupportResistanceDetector.class);

    private final SupportResistanceProperties properties;
    private final double[] fibonacciLevels = {0.236, 0.382, 0.5, 0.618, 0.786};

    // 🔥 新增：缓存支撑阻力位 - 按币种+时间周期分别存储
    private final Map<String, Map<String, List<SupportResistanceLevel>>> levelCache = new HashMap<>();
    
    // 🔥 新增：记录上次计算时间，避免重复计算（5分钟缓存）
    private final Map<String, Long> lastCalculationTimeMap = new HashMap<>();

    @Autowired
    private BinanceApiService binanceApiService;

    public SupportResistanceDetector(SupportResistanceProperties properties) {
        this.properties = properties;
    }

    /**
     * 🔥 修复：安全创建BigDecimal，处理币安API返回的异常数据
     * @param value 要转换的值
     * @return BigDecimal值，如果解析失败返回ZERO
     */
    private BigDecimal safeBigDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        
        // 如果已经是数字类型，直接转换
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        
        // 如果是字符串类型，安全处理
        if (value instanceof String) {
            String strValue = (String) value;
            if ("null".equals(strValue) || "N".equals(strValue) || strValue.trim().isEmpty()) {
                return BigDecimal.ZERO;
            }
            try {
                return new BigDecimal(strValue.trim());
            } catch (NumberFormatException e) {
                logger.warn("无法解析字符串为BigDecimal: '{}', 返回ZERO", strValue);
                return BigDecimal.ZERO;
            }
        }
        
        // 其他类型尝试转换为字符串再解析
        try {
            String strValue = String.valueOf(value);
            if ("N".equals(strValue)) {
                return BigDecimal.ZERO;
            }
            return new BigDecimal(strValue);
        } catch (Exception e) {
            logger.warn("无法解析{}类型为BigDecimal: '{}', 返回ZERO", value.getClass().getSimpleName(), value);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 🔥 修复：安全解析各种类型为double，处理null、Long、String等类型
     * @param value 要解析的值（可能是String、Long、Double、Integer等）
     * @return double值，如果解析失败返回0.0
     */
    private double safeParseDouble(Object value) {
        if (value == null) {
            return 0.0;
        }
        
        // 如果已经是数字类型，直接转换
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        
        // 如果是字符串类型，按原逻辑处理
        if (value instanceof String) {
            String strValue = (String) value;
            if ("null".equals(strValue) || "N".equals(strValue) || strValue.trim().isEmpty()) {
                return 0.0;
            }
            try {
                return Double.parseDouble(strValue.trim());
            } catch (NumberFormatException e) {
                logger.warn("无法解析字符串为double: '{}', 返回0.0", strValue);
                return 0.0;
            }
        }
        
        // 其他类型尝试转换为字符串再解析
        try {
            String strValue = String.valueOf(value);
            if ("N".equals(strValue)) {
                return 0.0;
            }
            return Double.parseDouble(strValue);
        } catch (Exception e) {
            logger.warn("无法解析{}类型为double: '{}', 返回0.0", value.getClass().getSimpleName(), value);
            return 0.0;
        }
    }

    /**
     * 支撑阻力位级别类
     */
    public static class SupportResistanceLevel {
        private final double price;
        private final double range;
        private final String type; // "SUPPORT" or "RESISTANCE"
        private final String timeframe;
        private int touchCount;
        private int bounceCount;
        private double volumeWeight;
        private double trendWeight;
        private long lastTouchTime;
        private boolean isValid;
        private List<Double> touchPrices;

        public SupportResistanceLevel(double price, double range, String type, String timeframe) {
            this.price = price;
            this.range = range;
            this.type = type;
            this.timeframe = timeframe;
            this.touchCount = 0;
            this.bounceCount = 0;
            this.volumeWeight = 0;
            this.trendWeight = 0;
            this.lastTouchTime = 0;
            this.isValid = true;
            this.touchPrices = new ArrayList<>();
        }

        public double getPrice() { return price; }
        public double getRange() { return range; }
        public String getType() { return type; }
        public String getTimeframe() { return timeframe; }
        public int getTouchCount() { return touchCount; }
        public int getBounceCount() { return bounceCount; }
        public double getVolumeWeight() { return volumeWeight; }
        public double getTrendWeight() { return trendWeight; }
        public long getLastTouchTime() { return lastTouchTime; }
        public boolean isValid() { return isValid; }
        public List<Double> getTouchPrices() { return touchPrices; }

        public void incrementTouchCount() { this.touchCount++; }
        public void incrementBounceCount() { this.bounceCount++; }
        public void setVolumeWeight(double weight) { this.volumeWeight = weight; }
        public void setTrendWeight(double weight) { this.trendWeight = weight; }
        public void updateLastTouchTime() { this.lastTouchTime = System.currentTimeMillis(); }
        public void setValid(boolean valid) { this.isValid = valid; }
        public void addTouchPrice(double price) { this.touchPrices.add(price); }

        public double calculateScore() {
            double score = 0;
            score += touchCount * 2;
            score += bounceCount * 3;
            score += volumeWeight * 1.5;
            score += trendWeight * 1.5;
            return score;
        }

        public boolean isInRange(double price) {
            return Math.abs(price - this.price) <= this.range;
        }
    }

    /**
     * 支撑阻力位结果类
     */
    public static class SupportResistanceResult {
        private final List<BigDecimal> supportLevels;
        private final List<BigDecimal> resistanceLevels;
        private final Map<String, List<BigDecimal>> supportDetails;
        private final Map<String, List<BigDecimal>> resistanceDetails;

        public SupportResistanceResult(List<BigDecimal> supportLevels, List<BigDecimal> resistanceLevels,
                                       Map<String, List<BigDecimal>> supportDetails, Map<String, List<BigDecimal>> resistanceDetails) {
            this.supportLevels = supportLevels;
            this.resistanceLevels = resistanceLevels;
            this.supportDetails = supportDetails;
            this.resistanceDetails = resistanceDetails;
        }

        public List<BigDecimal> getSupportLevels() {
            return supportLevels;
        }

        public List<BigDecimal> getResistanceLevels() {
            return resistanceLevels;
        }

        public Map<String, List<BigDecimal>> getSupportDetails() {
            return supportDetails;
        }

        public Map<String, List<BigDecimal>> getResistanceDetails() {
            return resistanceDetails;
        }

        /**
         * 获取最近的支撑位
         * @param currentPrice 当前价格
         * @return 最近的支撑位价格
         */
        public BigDecimal getNearestSupportLevel(BigDecimal currentPrice) {
            if (supportLevels.isEmpty()) {
                return BigDecimal.ZERO;
            }
            
            // 只考虑低于当前价格的支撑位
            return supportLevels.stream()
                .filter(level -> level.compareTo(currentPrice) < 0)
                .min((a, b) -> {
                    BigDecimal diffA = currentPrice.subtract(a).abs();
                    BigDecimal diffB = currentPrice.subtract(b).abs();
                    return diffA.compareTo(diffB);
                })
                .orElse(BigDecimal.ZERO);
        }

        /**
         * 获取最近的阻力位
         * @param currentPrice 当前价格
         * @return 最近的阻力位价格
         */
        public BigDecimal getNearestResistanceLevel(BigDecimal currentPrice) {
            if (resistanceLevels.isEmpty()) {
                return BigDecimal.valueOf(Double.MAX_VALUE);
            }
            
            // 只考虑高于当前价格的阻力位
            return resistanceLevels.stream()
                .filter(level -> level.compareTo(currentPrice) > 0)
                .min((a, b) -> {
                    BigDecimal diffA = currentPrice.subtract(a).abs();
                    BigDecimal diffB = currentPrice.subtract(b).abs();
                    return diffA.compareTo(diffB);
                })
                .orElse(BigDecimal.valueOf(Double.MAX_VALUE));
        }

        /**
         * 检查价格是否接近支撑位
         * @param currentPrice 当前价格
         * @param threshold 接近阈值（百分比）
         * @return 是否接近支撑位
         */
        public boolean isNearSupportLevel(BigDecimal currentPrice, double threshold) {
            BigDecimal nearestSupport = getNearestSupportLevel(currentPrice);
            if (nearestSupport.compareTo(BigDecimal.ZERO) == 0) {
                return false;
            }

            BigDecimal distance = currentPrice.subtract(nearestSupport).divide(currentPrice, 8, RoundingMode.HALF_UP);
            return distance.compareTo(BigDecimal.valueOf(threshold)) <= 0;
        }

        /**
         * 检查价格是否接近阻力位
         * @param currentPrice 当前价格
         * @param threshold 接近阈值（百分比）
         * @return 是否接近阻力位
         */
        public boolean isNearResistanceLevel(BigDecimal currentPrice, double threshold) {
            BigDecimal nearestResistance = getNearestResistanceLevel(currentPrice);
            if (nearestResistance.compareTo(BigDecimal.valueOf(Double.MAX_VALUE)) == 0) {
                return false;
            }

            BigDecimal distance = nearestResistance.subtract(currentPrice).divide(currentPrice, 8, RoundingMode.HALF_UP);
            return distance.compareTo(BigDecimal.valueOf(threshold)) <= 0;
        }

        /**
         * 检查价格是否突破支撑位
         * @param currentPrice 当前价格
         * @param previousPrice 前一价格
         * @return 是否突破支撑位
         */
        public boolean isBreakingSupport(BigDecimal currentPrice, BigDecimal previousPrice) {
            for (BigDecimal level : supportLevels) {
                if (previousPrice.compareTo(level) >= 0 && currentPrice.compareTo(level) < 0) {
                    return true;
                }
            }
            return false;
        }

        /**
         * 检查价格是否突破阻力位
         * @param currentPrice 当前价格
         * @param previousPrice 前一价格
         * @return 是否突破阻力位
         */
        public boolean isBreakingResistance(BigDecimal currentPrice, BigDecimal previousPrice) {
            for (BigDecimal level : resistanceLevels) {
                if (previousPrice.compareTo(level) <= 0 && currentPrice.compareTo(level) > 0) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * 检测支撑阻力位
     * @param symbol 币种符号
     * @param timeframe 时间周期
     * @return 支撑阻力位结果
     */
    public SupportResistanceResult detectLevels(String symbol, String timeframe) {
        try {
            // 获取K线数据
            List<List<String>> klinesString = binanceApiService.getKlines(symbol, timeframe, 200);
            if (klinesString == null || klinesString.isEmpty()) {
                logger.error("[{}] K线数据为空", symbol);
                return null;
            }

            // 将List<List<String>>转换为List<double[]> - 修复类型转换问题
            List<double[]> ohlcvList = klinesString.stream()
                    .map(kline -> {
                        double[] ohlcv = new double[6]; // open, high, low, close, volume, closeTime
                        // 🔥 修复：安全转换，处理null和"null"字符串
                        try {
                            ohlcv[0] = safeParseDouble(kline.get(1)); // open
                            ohlcv[1] = safeParseDouble(kline.get(2)); // high
                            ohlcv[2] = safeParseDouble(kline.get(3)); // low
                            ohlcv[3] = safeParseDouble(kline.get(4)); // close
                            ohlcv[4] = safeParseDouble(kline.get(5)); // volume
                            ohlcv[5] = safeParseDouble(kline.get(6)); // closeTime
                        } catch (Exception e) {
                            logger.warn("[{}] K线数据解析异常，跳过此根K线: {}", symbol, e.getMessage());
                            // 返回无效数据，后续过滤
                            ohlcv[0] = ohlcv[1] = ohlcv[2] = ohlcv[3] = ohlcv[4] = ohlcv[5] = -1;
                        }
                        return ohlcv;
                    })
                    .filter(ohlcv -> ohlcv[3] > 0) // 过滤无效数据
                    .collect(Collectors.toList());
            
            return detectLevels(ohlcvList, timeframe, symbol, 0, ohlcvList.size() - 1);
        } catch (Exception e) {
            logger.error("[{}] 检测支撑阻力位异常: {}", symbol, e.getMessage());
            return null;
        }
    }

    /**
     * 🔥 优化：检测支撑阻力位（核心方法）- 增加缓存机制和更严格的过滤
     * @param ohlcvList K线数据列表
     * @param timeframe 时间周期
     * @param symbol 币种符号
     * @param priceOffset 价格偏移量
     * @param idx 索引
     * @return 支撑阻力位结果
     */
    public SupportResistanceResult detectLevels(List<double[]> ohlcvList, String timeframe, String symbol, int priceOffset, int idx) {
        if (ohlcvList == null || ohlcvList.isEmpty()) {
            logger.error("[{}] K线数据为空", symbol);
            return null;
        }

        // 🔥 新增：缓存检查逻辑 - 避免5分钟内重复计算
        String cacheKey = symbol + "_" + timeframe;
        long currentTime = System.currentTimeMillis();
        Long lastCalculationTime = lastCalculationTimeMap.get(cacheKey);
        
        // 如果5分钟内已经计算过，直接返回缓存结果
        if (lastCalculationTime != null && (currentTime - lastCalculationTime) < 300000) { // 5分钟缓存
            Map<String, List<SupportResistanceLevel>> symbolCache = levelCache.get(cacheKey);
            if (symbolCache != null) {
                List<SupportResistanceLevel> cachedSupport = symbolCache.get("SUPPORT");
                List<SupportResistanceLevel> cachedResistance = symbolCache.get("RESISTANCE");
                
                if (cachedSupport != null && cachedResistance != null) {
                    logger.debug("[{}] 使用缓存的支撑阻力位数据 ({})", symbol, timeframe);
                    
                    // 转换缓存数据为结果格式
                    List<BigDecimal> supportPrices = cachedSupport.stream()
                        .map(level -> BigDecimal.valueOf(level.getPrice()))
                        .sorted()
                        .collect(Collectors.toList());
                    
                    List<BigDecimal> resistancePrices = cachedResistance.stream()
                        .map(level -> BigDecimal.valueOf(level.getPrice()))
                        .sorted()
                        .collect(Collectors.toList());
                    
                    return new SupportResistanceResult(supportPrices, resistancePrices, new HashMap<>(), new HashMap<>());
                }
            }
        }
        
        // 严格的边界检查
        if (priceOffset < 0 || idx < 2 || priceOffset + idx >= ohlcvList.size() || priceOffset + idx - 2 < 0) {
            logger.error("[{}] 索引参数错误: priceOffset={}, idx={}, ohlcvList.size()={}, 调整为安全值", 
                        symbol, priceOffset, idx, ohlcvList.size());
            if (ohlcvList.size() < 3) {
                logger.warn("[{}] K线数据不足3根，无法检测支撑阻力位", symbol);
                return null;
            }
            priceOffset = 0;
            idx = ohlcvList.size() - 1;
            if (idx < 2) {
                logger.warn("[{}] 调整后索引仍不安全: idx={}, 无法检测支撑阻力位", symbol, idx);
                return null;
            }
        }
        
        logger.info("[{}] 开始检测支撑阻力位，时间周期: {}, 索引参数: priceOffset={}, idx={}", symbol, timeframe, priceOffset, idx);
        
        // 🔥 修复：安全获取当前价格，防止"N"字符异常
        double currentPriceDouble = ohlcvList.get(priceOffset + idx)[3]; // close price
        if (currentPriceDouble <= 0) {
            logger.warn("[{}] 当前价格无效: {}, 无法检测支撑阻力位", symbol, currentPriceDouble);
            return null;
        }
        BigDecimal currentPrice = BigDecimal.valueOf(currentPriceDouble);
        
        // 检测支撑位和阻力位
        Set<SupportResistanceLevel> supportLevels = new HashSet<>();
        Set<SupportResistanceLevel> resistanceLevels = new HashSet<>();
        
        // 🔥 优化：使用更智能的检测算法，减少噪声
        // 遍历K线数据，检测支撑位和阻力位
        for (int i = 2; i < ohlcvList.size() - 2; i++) { // 🔥 改进：增加前后缓冲，提高准确性
            double[] current = ohlcvList.get(i);
            double[] previous = ohlcvList.get(i - 1);
            double[] next = ohlcvList.get(i + 1);
            double[] prev2 = ohlcvList.get(i - 2); // 🔥 新增：更多历史数据
            double[] next2 = ohlcvList.get(i + 2); // 🔥 新增：更多未来数据
            
            // 🔥 新增：验证K线数据有效性，跳过无效数据
            if (!isValidOHLCV(current) || !isValidOHLCV(previous) || !isValidOHLCV(next) ||
                !isValidOHLCV(prev2) || !isValidOHLCV(next2)) {
                logger.debug("[{}] 跳过无效K线数据，索引: {}", symbol, i);
                continue;
            }
            
            // 🔥 优化：更严格的支撑位检测 - 要求更明显的低点
            if (isPotentialSupportOptimized(current, previous, next, prev2, next2)) {
                double supportPrice = current[2]; // 最低价
                if (supportPrice < currentPrice.doubleValue() && supportPrice > 0) {
                    double range = calculateDynamicRangeOptimized(supportPrice, symbol);
                    checkAndAddLevel(supportLevels, supportPrice, range, "SUPPORT", timeframe, ohlcvList, i);
                }
            }
            
            // 🔥 优化：更严格的阻力位检测 - 要求更明显的高点
            if (isPotentialResistanceOptimized(current, previous, next, prev2, next2)) {
                double resistancePrice = current[1]; // 最高价
                if (resistancePrice > currentPrice.doubleValue() && resistancePrice > 0) {
                    double range = calculateDynamicRangeOptimized(resistancePrice, symbol);
                    checkAndAddLevel(resistanceLevels, resistancePrice, range, "RESISTANCE", timeframe, ohlcvList, i);
                }
            }
        }
        
        // 🔥 可选：添加传统枢轴点作为额外的支撑阻力位（如果需要的话）
        // addPivotPoints(ohlcvList, supportLevels, resistanceLevels, currentPrice.doubleValue(), symbol);
        
        // 计算成交量权重
        calculateVolumeWeights(supportLevels, resistanceLevels, ohlcvList);
        
        // 计算趋势权重
        calculateTrendWeights(supportLevels, resistanceLevels, ohlcvList);
        
        // 🔥 优化：更严格的合并相近水平
        supportLevels = mergeLevelsOptimized(supportLevels, symbol);
        resistanceLevels = mergeLevelsOptimized(resistanceLevels, symbol);
        
        // 转换为结果格式
        List<BigDecimal> supportPrices = supportLevels.stream()
            .map(level -> BigDecimal.valueOf(level.getPrice()))
            .sorted()
            .collect(Collectors.toList());
            
        List<BigDecimal> resistancePrices = resistanceLevels.stream()
            .map(level -> BigDecimal.valueOf(level.getPrice()))
            .sorted()
            .collect(Collectors.toList());
        
        // 记录详细信息
        Map<String, List<BigDecimal>> supportDetails = new HashMap<>();
        Map<String, List<BigDecimal>> resistanceDetails = new HashMap<>();
        
        // 添加当前价格信息
        supportDetails.put("currentPrice", Collections.singletonList(currentPrice));
        resistanceDetails.put("currentPrice", Collections.singletonList(currentPrice));
        
        // 记录检测到的水平
        logger.info("[{}] 当前价格: {}", symbol, currentPrice);
        logLevels(symbol, "支撑位", supportPrices, timeframe);
        logLevels(symbol, "阻力位", resistancePrices, timeframe);
        
        // 🔥 更新缓存
        Map<String, List<SupportResistanceLevel>> symbolCache = levelCache.computeIfAbsent(cacheKey, k -> new HashMap<>());
        symbolCache.put("SUPPORT", new ArrayList<>(supportLevels));
        symbolCache.put("RESISTANCE", new ArrayList<>(resistanceLevels));
        
        // 🔥 记录计算时间
        lastCalculationTimeMap.put(cacheKey, currentTime);
        
        return new SupportResistanceResult(supportPrices, resistancePrices, supportDetails, resistanceDetails);
    }

    /**
     * 🔥 优化：更严格的支撑位检测条件
     */
    private boolean isPotentialSupportOptimized(double[] current, double[] previous, double[] next, double[] prev2, double[] next2) {
        // 当前K线的最低价必须是5根K线中的最低点
        double currentLow = current[2];
        return currentLow <= previous[2] && currentLow <= next[2] && 
               currentLow <= prev2[2] && currentLow <= next2[2] &&
               // 🔥 新增：要求明显的价格差异（至少0.5%）
               (previous[2] - currentLow) / currentLow > 0.005 &&
               (next[2] - currentLow) / currentLow > 0.005;
    }

    /**
     * 🔥 优化：更严格的阻力位检测条件
     */
    private boolean isPotentialResistanceOptimized(double[] current, double[] previous, double[] next, double[] prev2, double[] next2) {
        // 当前K线的最高价必须是5根K线中的最高点
        double currentHigh = current[1];
        return currentHigh >= previous[1] && currentHigh >= next[1] && 
               currentHigh >= prev2[1] && currentHigh >= next2[1] &&
               // 🔥 新增：要求明显的价格差异（至少0.5%）
               (currentHigh - previous[1]) / previous[1] > 0.005 &&
               (currentHigh - next[1]) / next[1] > 0.005;
    }

    /**
     * 🔥 优化：智能动态区间计算
     * 根据币种价格范围和波动性调整
     */
    private double calculateDynamicRangeOptimized(double price, String symbol) {
        // 🔥 基于参考项目的智能区间计算
        if (price > 1000) { 
            // BTC/ETH等高价币：使用较小的区间
            return price * 0.015; // 1.5%区间（比原来的0.01%大，减少密集度）
        } else if (price > 100) {
            // SOL等中高价币
            return price * 0.025; // 2.5%区间
        } else if (price > 10) {
            // UNI/LINK等中价币
            return price * 0.03; // 3%区间
        } else if (price > 1) {
            // DOGE/ADA等较低价币
            return price * 0.04; // 4%区间
        } else {
            // 极低价币
            return price * 0.05; // 5%区间
        }
    }

    /**
     * 🔥 优化：更严格的合并逻辑
     */
    private Set<SupportResistanceLevel> mergeLevelsOptimized(Set<SupportResistanceLevel> levels, String symbol) {
        if (levels.isEmpty()) {
            return levels;
        }

        List<SupportResistanceLevel> sortedLevels = new ArrayList<>(levels);
        sortedLevels.sort(Comparator.comparingDouble(SupportResistanceLevel::getPrice));
        
        Set<SupportResistanceLevel> mergedLevels = new HashSet<>();
        SupportResistanceLevel currentLevel = sortedLevels.get(0);
        
        for (int i = 1; i < sortedLevels.size(); i++) {
            SupportResistanceLevel nextLevel = sortedLevels.get(i);
            
            // 🔥 优化：使用更智能的合并区间
            double range = calculateDynamicRangeOptimized(currentLevel.getPrice(), symbol);
            
            // 如果价格在区间内，合并这两个水平
            if (Math.abs(nextLevel.getPrice() - currentLevel.getPrice()) <= range) {
                // 合并逻辑：保留权重更高的水平作为代表
                if (nextLevel.calculateScore() > currentLevel.calculateScore()) {
                    currentLevel = nextLevel;
                }
                currentLevel.incrementTouchCount(); // 增加触及次数
            } else {
                // 如果价格不在区间内，保存当前水平并开始新的水平
                mergedLevels.add(currentLevel);
                currentLevel = nextLevel;
            }
        }
        
        // 添加最后一个水平
        mergedLevels.add(currentLevel);
        
        // 🔥 关键优化：降低触及次数门槛，增加可用位置
        return mergedLevels.stream()
            .filter(level -> level.getTouchCount() >= 3) // 🔥 降低：从3次改为2次触及
            .filter(level -> level.calculateScore() >= 4.0) // 🔥 降低：从5.0改为3.0综合得分门槛
            .collect(Collectors.toSet());
    }

    /**
     * 🔥 新增：验证OHLCV数据有效性
     * @param ohlcv OHLCV数据数组
     * @return 是否有效
     */
    private boolean isValidOHLCV(double[] ohlcv) {
        if (ohlcv == null || ohlcv.length < 6) {
            return false;
        }
        
        // 检查价格数据有效性
        for (int i = 0; i < 4; i++) { // open, high, low, close
            if (ohlcv[i] <= 0 || Double.isNaN(ohlcv[i]) || Double.isInfinite(ohlcv[i])) {
                return false;
            }
        }
        
        // 检查成交量
        if (ohlcv[4] < 0 || Double.isNaN(ohlcv[4]) || Double.isInfinite(ohlcv[4])) {
            return false;
        }
        
        // 检查价格逻辑：最高价应该 >= 最低价
        if (ohlcv[1] < ohlcv[2]) { // high < low
            return false;
        }
        
        return true;
    }

    /**
     * 记录支撑阻力位日志
     */
    private void logLevels(String symbol, String type, List<BigDecimal> levels, String timeframe) {
        if (levels.isEmpty()) {
            logger.info("[{}] {} ({}): 无", symbol, type, timeframe);
            return;
        }
        
        // 🔥 优化：根据价格范围智能格式化，减少日志冗余
        String formattedLevels = levels.stream()
                .limit(5) // 🔥 只显示最重要的5个位置，避免日志过长
                .map(level -> {
                    double price = level.doubleValue();
                    if (price >= 1000) {
                        return String.format("%.2f", price); // BTC/ETH等高价币：2位小数
                    } else if (price >= 100) {
                        return String.format("%.2f", price); // SOL等中价币：2位小数
                    } else if (price >= 10) {
                        return String.format("%.3f", price); // UNI等：3位小数
                    } else if (price >= 1) {
                        return String.format("%.4f", price); // DOGE等：4位小数
                    } else if (price >= 0.1) {
                        return String.format("%.5f", price); // 小价币：5位小数
                    } else {
                        return String.format("%.6f", price); // 极小价币：6位小数
                    }
                })
                .collect(Collectors.joining(", "));
        
        String suffix = levels.size() > 5 ? String.format(" ...等%d个", levels.size()) : "";
        logger.info("[{}] {} ({}): {}{}", symbol, type, timeframe, formattedLevels, suffix);
    }

    /**
     * 🔥 保留：计算成交量权重
     */
    private void calculateVolumeWeights(Set<SupportResistanceLevel> supportLevels,
                                        Set<SupportResistanceLevel> resistanceLevels, List<double[]> ohlcvList) {
        // 计算平均成交量
        double avgVolume = ohlcvList.stream()
            .mapToDouble(kline -> kline[4])
            .average()
            .orElse(0);
            
        // 🔥 优化：为支撑位计算成交量权重
        for (SupportResistanceLevel level : supportLevels) {
            double volumeWeight = level.getTouchPrices().stream()
                .mapToDouble(price -> {
                    // 找到最接近的K线
                    return ohlcvList.stream()
                        .filter(kline -> Math.abs(kline[3] - price) < level.getRange()) // 使用收盘价
                        .mapToDouble(kline -> kline[4]) // 成交量
                        .average()
                        .orElse(0);
                })
                .average()
                .orElse(0);
                
            level.setVolumeWeight(avgVolume > 0 ? volumeWeight / avgVolume : 0);
        }
        
        // 🔥 优化：为阻力位计算成交量权重
        for (SupportResistanceLevel level : resistanceLevels) {
            double volumeWeight = level.getTouchPrices().stream()
                .mapToDouble(price -> {
                    return ohlcvList.stream()
                        .filter(kline -> Math.abs(kline[3] - price) < level.getRange()) // 使用收盘价
                        .mapToDouble(kline -> kline[4]) // 成交量
                        .average()
                        .orElse(0);
                })
                .average()
                .orElse(0);
                
            level.setVolumeWeight(avgVolume > 0 ? volumeWeight / avgVolume : 0);
        }
    }

    /**
     * 🔥 保留：计算趋势权重
     */
    private void calculateTrendWeights(Set<SupportResistanceLevel> supportLevels,
                                       Set<SupportResistanceLevel> resistanceLevels, List<double[]> ohlcvList) {
        // 🔥 优化：计算20周期SMA判断趋势
        List<Double> sma20 = calculateSMA(ohlcvList, 20);
        if (sma20 == null || sma20.isEmpty()) {
            return;
        }
        
        // 判断整体趋势
        boolean isUptrend = isUptrend(ohlcvList);
        
        // 🔥 优化：为支撑位设置趋势权重
        for (SupportResistanceLevel level : supportLevels) {
            // 支撑位在上升趋势中权重更高
            level.setTrendWeight(isUptrend ? 1.5 : 0.5);
        }
        
        // 🔥 优化：为阻力位设置趋势权重
        for (SupportResistanceLevel level : resistanceLevels) {
            // 阻力位在下降趋势中权重更高
            level.setTrendWeight(isUptrend ? 0.5 : 1.5);
        }
    }

    /**
     * 🔥 保留：计算SMA
     */
    private List<Double> calculateSMA(List<double[]> ohlcvList, int period) {
        if (ohlcvList.size() < period) {
            return null;
        }
        
        List<Double> sma = new ArrayList<>();
        for (int i = period - 1; i < ohlcvList.size(); i++) {
            double sum = 0;
            for (int j = 0; j < period; j++) {
                sum += ohlcvList.get(i - j)[3]; // 收盘价
            }
            sma.add(sum / period);
        }
        
        return sma;
    }

    /**
     * 🔥 保留：判断是否为上升趋势
     */
    private boolean isUptrend(List<double[]> ohlcvList) {
        if (ohlcvList.size() < 20) {
            return false;
        }
        
        List<Double> sma20 = calculateSMA(ohlcvList, 20);
        if (sma20 == null || sma20.size() < 2) {
            return false;
        }
        
        // 判断SMA20是否向上倾斜
        return sma20.get(sma20.size() - 1) > sma20.get(sma20.size() - 2);
    }

    /**
     * 🔥 保留：检查并添加支撑阻力位
     */
    private void checkAndAddLevel(Set<SupportResistanceLevel> levels, double price, double range,
                                String type, String timeframe, List<double[]> ohlcvList, int index) {
        Optional<SupportResistanceLevel> existingLevel = levels.stream()
            .filter(level -> level.isInRange(price))
            .findFirst();
            
        if (existingLevel.isPresent()) {
            SupportResistanceLevel level = existingLevel.get();
            level.incrementTouchCount();
            level.addTouchPrice(price);
            level.updateLastTouchTime();
            
            if (hasBounce(ohlcvList, index, type)) {
                level.incrementBounceCount();
            }
        } else {
            SupportResistanceLevel newLevel = new SupportResistanceLevel(price, range, type, timeframe);
            newLevel.incrementTouchCount();
            newLevel.addTouchPrice(price);
            newLevel.updateLastTouchTime();
            levels.add(newLevel);
        }
    }

    /**
     * 🔥 保留：检查是否有反弹/回撤
     */
    private boolean hasBounce(List<double[]> ohlcvList, int index, String type) {
        if (index < 2 || index >= ohlcvList.size() - 2) {
            return false;
        }
        
        double[] current = ohlcvList.get(index);
        double[] next = ohlcvList.get(index + 1);
        
        if (type.equals("SUPPORT")) {
            return (next[3] - current[2]) / current[2] > 0.01; // next close - current low
        } else {
            return (current[1] - next[3]) / current[1] > 0.01; // current high - next close
        }
    }

    /**
     * 🔥 优化：获取最近的支撑位（缓存版本）
     * @param symbol 币种符号
     * @param timeframe 时间周期
     * @param currentPrice 当前价格
     * @return 最近的支撑位价格
     */
    public BigDecimal getLatestSupport(String symbol, String timeframe, BigDecimal currentPrice) {
        String cacheKey = symbol + "_" + timeframe;
        Map<String, List<SupportResistanceLevel>> symbolCache = levelCache.get(cacheKey);
        
        if (symbolCache == null || !symbolCache.containsKey("SUPPORT")) {
            SupportResistanceResult result = detectLevels(symbol, timeframe);
            if (result == null) return BigDecimal.ZERO;
            return result.getNearestSupportLevel(currentPrice);
        }
        
        List<SupportResistanceLevel> supports = symbolCache.get("SUPPORT");
        return supports.stream()
                .filter(level -> BigDecimal.valueOf(level.getPrice()).compareTo(currentPrice) < 0)
                .map(level -> BigDecimal.valueOf(level.getPrice()))
                .min((a, b) -> currentPrice.subtract(a).abs().compareTo(currentPrice.subtract(b).abs()))
                .orElse(BigDecimal.ZERO);
    }

    /**
     * 🔥 优化：获取最近的阻力位（缓存版本）
     * @param symbol 币种符号
     * @param timeframe 时间周期
     * @param currentPrice 当前价格
     * @return 最近的阻力位价格
     */
    public BigDecimal getLatestResistance(String symbol, String timeframe, BigDecimal currentPrice) {
        String cacheKey = symbol + "_" + timeframe;
        Map<String, List<SupportResistanceLevel>> symbolCache = levelCache.get(cacheKey);
        
        if (symbolCache == null || !symbolCache.containsKey("RESISTANCE")) {
            SupportResistanceResult result = detectLevels(symbol, timeframe);
            if (result == null) return BigDecimal.valueOf(Double.MAX_VALUE);
            return result.getNearestResistanceLevel(currentPrice);
        }

        List<SupportResistanceLevel> resistances = symbolCache.get("RESISTANCE");
        return resistances.stream()
                .filter(level -> BigDecimal.valueOf(level.getPrice()).compareTo(currentPrice) > 0)
                .map(level -> BigDecimal.valueOf(level.getPrice()))
                .min((a, b) -> currentPrice.subtract(a).abs().compareTo(currentPrice.subtract(b).abs()))
                .orElse(BigDecimal.valueOf(Double.MAX_VALUE));
    }

    /**
     * 🔥 新增：多时间周期获取阻力位（避免4小时无阻力位的情况）
     * @param symbol 币种符号
     * @param currentPrice 当前价格
     * @return 最近的阻力位价格
     */
    public BigDecimal getMultiTimeframeResistance(String symbol, BigDecimal currentPrice) {
        try {
            // 优先级1：4小时阻力位
            BigDecimal resistance4h = getLatestResistance(symbol, "4h", currentPrice);
            if (resistance4h.compareTo(BigDecimal.valueOf(Double.MAX_VALUE)) < 0) {
                logger.debug("[{}] 使用4小时阻力位: {}", symbol, resistance4h.toPlainString());
                return resistance4h;
            }

            // 优先级2：日线阻力位
            BigDecimal resistance1d = getLatestResistance(symbol, "1d", currentPrice);
            if (resistance1d.compareTo(BigDecimal.valueOf(Double.MAX_VALUE)) < 0) {
                logger.info("[{}] 4小时无阻力位，使用日线阻力位: {}", symbol, resistance1d.toPlainString());
                return resistance1d;
            }

            // 优先级3：周线阻力位
            BigDecimal resistance1w = getLatestResistance(symbol, "1w", currentPrice);
            if (resistance1w.compareTo(BigDecimal.valueOf(Double.MAX_VALUE)) < 0) {
                logger.info("[{}] 4小时和日线无阻力位，使用周线阻力位: {}", symbol, resistance1w.toPlainString());
                return resistance1w;
            }

            // 都没有找到，返回最大值（表示无阻力位）
            logger.warn("[{}] 多时间周期均无阻力位检测结果", symbol);
            return BigDecimal.valueOf(Double.MAX_VALUE);

        } catch (Exception e) {
            logger.error("[{}] 多时间周期阻力位检测异常: {}", symbol, e.getMessage());
            return BigDecimal.valueOf(Double.MAX_VALUE);
        }
    }

    /**
     * 🔥 新增：多时间周期获取支撑位（避免4小时无支撑位的情况）
     * @param symbol 币种符号
     * @param currentPrice 当前价格
     * @return 最近的支撑位价格
     */
    public BigDecimal getMultiTimeframeSupport(String symbol, BigDecimal currentPrice) {
        try {
            // 优先级1：4小时支撑位
            BigDecimal support4h = getLatestSupport(symbol, "4h", currentPrice);
            if (support4h.compareTo(BigDecimal.ZERO) > 0) {
                logger.debug("[{}] 使用4小时支撑位: {}", symbol, support4h.toPlainString());
                return support4h;
            }

            // 优先级2：日线支撑位
            BigDecimal support1d = getLatestSupport(symbol, "1d", currentPrice);
            if (support1d.compareTo(BigDecimal.ZERO) > 0) {
                logger.info("[{}] 4小时无支撑位，使用日线支撑位: {}", symbol, support1d.toPlainString());
                return support1d;
            }

            // 优先级3：周线支撑位
            BigDecimal support1w = getLatestSupport(symbol, "1w", currentPrice);
            if (support1w.compareTo(BigDecimal.ZERO) > 0) {
                logger.info("[{}] 4小时和日线无支撑位，使用周线支撑位: {}", symbol, support1w.toPlainString());
                return support1w;
            }

            // 都没有找到，返回0（表示无支撑位）
            logger.warn("[{}] 多时间周期均无支撑位检测结果", symbol);
            return BigDecimal.ZERO;

        } catch (Exception e) {
            logger.error("[{}] 多时间周期支撑位检测异常: {}", symbol, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 🔥 新增：清除缓存方法（用于测试或手动刷新）
     * @param symbol 币种符号
     * @param timeframe 时间周期
     */
    public void clearCache(String symbol, String timeframe) {
        String cacheKey = symbol + "_" + timeframe;
        levelCache.remove(cacheKey);
        lastCalculationTimeMap.remove(cacheKey);
        logger.info("[{}] 清除支撑阻力位缓存 ({})", symbol, timeframe);
    }

    /**
     * 🔥 新增：获取缓存状态信息
     * @return 缓存状态信息
     */
    public Map<String, Object> getCacheStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("cacheSize", levelCache.size());
        status.put("calculationTimes", lastCalculationTimeMap.size());
        
        // 统计各个时间周期的缓存情况
        Map<String, Integer> timeframeCounts = new HashMap<>();
        for (String cacheKey : levelCache.keySet()) {
            String timeframe = cacheKey.substring(cacheKey.lastIndexOf("_") + 1);
            timeframeCounts.put(timeframe, timeframeCounts.getOrDefault(timeframe, 0) + 1);
        }
        status.put("timeframeCounts", timeframeCounts);
        
        return status;
    }
}


