package com.trading.bot.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 交易策略配置类
 */
@Configuration
@ConfigurationProperties(prefix = "trading")
public class TradingConfig {

    private Strategy strategy = new Strategy();
    private Risk risk = new Risk();
    private AddPosition addPosition = new AddPosition();
    private List<String> symbols; // 新增：交易对列表
    
    // 币安API配置
    private String apiKey;
    private String secretKey;
    private String baseUrl = "https://testnet.binancefuture.com";
    private boolean testnet = true;
    private String strategyName = "MA25Strategy";
    private int leverage = 20;

    // 新增：动态仓位管理配置
    private DynamicPosition dynamicPosition = new DynamicPosition();

    public Strategy getStrategy() {
        return strategy;
    }

    public void setStrategy(Strategy strategy) {
        this.strategy = strategy;
    }

    public Risk getRisk() {
        return risk;
    }

    public void setRisk(Risk risk) {
        this.risk = risk;
    }

    public AddPosition getAddPosition() {
        return addPosition;
    }

    public void setAddPosition(AddPosition addPosition) {
        this.addPosition = addPosition;
    }

    public List<String> getSymbols() {
        return symbols;
    }

    public void setSymbols(List<String> symbols) {
        this.symbols = symbols;
    }

    // 方便外部直接访问的快捷方法
    public BigDecimal getInitialPositionUsdt() {
        return strategy.getInitialPositionUsdt();
    }

    public BigDecimal getAddPositionUsdt() {
        return addPosition.getAddPositionUsdt();
    }

    public BigDecimal getBasePositionUsdt() {
        return risk.getBasePositionUsdt();
    }

    public int getMa25Period() {
        return strategy.getMa25();
    }

    public int getMa25LookbackPeriods() {
        return strategy.getMa25LookbackPeriods();
    }

    public int getMa25TurnPeriod() {
        return strategy.getMa25TurnPeriod();
    }

    public int getConfirmationPeriods() {
        return strategy.getConfirmationPeriods();
    }

    // 币安API配置的getter和setter方法
    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public boolean isTestnet() {
        return testnet;
    }

    public void setTestnet(boolean testnet) {
        this.testnet = testnet;
    }

    public String getStrategyName() {
        return strategyName;
    }

    public void setStrategyName(String strategyName) {
        this.strategyName = strategyName;
    }

    public int getLeverage() {
        return leverage;
    }

    public void setLeverage(int leverage) {
        this.leverage = leverage;
    }

    /**
     * 策略配置
     */
    public static class Strategy {
        private String symbol = "BTCUSDT";
        private String timeframe = "15m";
        private int maPeriod = 25;
        private int confirmationPeriods = 2;
        private BigDecimal initialPositionUsdt = BigDecimal.valueOf(100);
        private int ma25 = 25; // 新增：MA25周期
        private int ma25LookbackPeriods = 50; // 新增：MA25回看周期
        private int ma25TurnPeriod = 3; // 新增：MA25调头判断周期

        public String getSymbol() {
            return symbol;
        }

        public void setSymbol(String symbol) {
            this.symbol = symbol;
        }

        public String getTimeframe() {
            return timeframe;
        }

        public void setTimeframe(String timeframe) {
            this.timeframe = timeframe;
        }

        public int getMaPeriod() {
            return maPeriod;
        }

        public void setMaPeriod(int maPeriod) {
            this.maPeriod = maPeriod;
        }

        public int getConfirmationPeriods() {
            return confirmationPeriods;
        }

        public void setConfirmationPeriods(int confirmationPeriods) {
            this.confirmationPeriods = confirmationPeriods;
        }

        public BigDecimal getInitialPositionUsdt() {
            return initialPositionUsdt;
        }

        public void setInitialPositionUsdt(BigDecimal initialPositionUsdt) {
            this.initialPositionUsdt = initialPositionUsdt;
        }

        public int getMa25() {
            return ma25;
        }

        public void setMa25(int ma25) {
            this.ma25 = ma25;
        }

        public int getMa25LookbackPeriods() {
            return ma25LookbackPeriods;
        }

        public void setMa25LookbackPeriods(int ma25LookbackPeriods) {
            this.ma25LookbackPeriods = ma25LookbackPeriods;
        }

        public int getMa25TurnPeriod() {
            return ma25TurnPeriod;
        }

        public void setMa25TurnPeriod(int ma25TurnPeriod) {
            this.ma25TurnPeriod = ma25TurnPeriod;
        }
    }

    /**
     * 风险管理配置
     */
    public static class Risk {
        private BigDecimal maxLossUsdt = BigDecimal.valueOf(80.0);
        private BigDecimal profitTakeThreshold = BigDecimal.valueOf(5.0);
        private BigDecimal basePositionUsdt = BigDecimal.valueOf(20.0);
        private int maxPositionMultiplier = 3; // 新增：最大仓位倍数
        private int maxAddCount = 2; // 新增：最大加仓次数
        private int maxHoldingHours = 8760; // 新增：最大持仓时间（1年）
        private BigDecimal minProfitPercentage = BigDecimal.valueOf(1.0); // 新增：最小利润百分比
        private int cooldownMinutes = 30; // 新增：开仓冷却期
        
        // 🔥 新增：动态止损配置
        private BigDecimal dynamicStopLossPercentage = BigDecimal.valueOf(0.15); // 动态止损百分比15%
        private BigDecimal maxDynamicStopLossUsdt = BigDecimal.valueOf(50.0); // 最大动态止损金额50U
        private boolean enableDynamicStopLoss = false; // 是否启用动态止损（已禁用）

        // 🔥 新增：维加斯通道追高追低保护配置（已调优）
        private boolean enableVegasAntiChasing = true; // 是否启用追高追低保护
        private BigDecimal shortTermRiskThreshold = BigDecimal.valueOf(0.05); // 短期风险阈值5%（降低追高风险）
        private BigDecimal mediumTermRiskThreshold = BigDecimal.valueOf(0.12); // 中期风险阈值12%（更严格控制）
        private BigDecimal minDynamicThreshold = BigDecimal.valueOf(0.003); // 最小动态阈值0.3%（更精细调整）
        private BigDecimal maxDynamicThreshold = BigDecimal.valueOf(0.025); // 最大动态阈值2.5%（降低波动）

        public BigDecimal getMaxLossUsdt() {
            return maxLossUsdt;
        }

        public void setMaxLossUsdt(BigDecimal maxLossUsdt) {
            this.maxLossUsdt = maxLossUsdt;
        }

        public BigDecimal getProfitTakeThreshold() {
            return profitTakeThreshold;
        }

        public void setProfitTakeThreshold(BigDecimal profitTakeThreshold) {
            this.profitTakeThreshold = profitTakeThreshold;
        }

        public BigDecimal getBasePositionUsdt() {
            return basePositionUsdt;
        }

        public void setBasePositionUsdt(BigDecimal basePositionUsdt) {
            this.basePositionUsdt = basePositionUsdt;
        }

        public int getMaxPositionMultiplier() {
            return maxPositionMultiplier;
        }

        public void setMaxPositionMultiplier(int maxPositionMultiplier) {
            this.maxPositionMultiplier = maxPositionMultiplier;
        }

        public int getMaxAddCount() {
            return maxAddCount;
        }

        public void setMaxAddCount(int maxAddCount) {
            this.maxAddCount = maxAddCount;
        }

        public int getMaxHoldingHours() {
            return maxHoldingHours;
        }

        public void setMaxHoldingHours(int maxHoldingHours) {
            this.maxHoldingHours = maxHoldingHours;
        }

        public BigDecimal getMinProfitPercentage() {
            return minProfitPercentage;
        }

        public void setMinProfitPercentage(BigDecimal minProfitPercentage) {
            this.minProfitPercentage = minProfitPercentage;
        }

        public int getCooldownMinutes() {
            return cooldownMinutes;
        }

        public void setCooldownMinutes(int cooldownMinutes) {
            this.cooldownMinutes = cooldownMinutes;
        }
        
        // 🔥 新增：动态止损配置的getter和setter
        public BigDecimal getDynamicStopLossPercentage() {
            return dynamicStopLossPercentage;
        }

        public void setDynamicStopLossPercentage(BigDecimal dynamicStopLossPercentage) {
            this.dynamicStopLossPercentage = dynamicStopLossPercentage;
        }

        public BigDecimal getMaxDynamicStopLossUsdt() {
            return maxDynamicStopLossUsdt;
        }

        public void setMaxDynamicStopLossUsdt(BigDecimal maxDynamicStopLossUsdt) {
            this.maxDynamicStopLossUsdt = maxDynamicStopLossUsdt;
        }

        public boolean isEnableDynamicStopLoss() {
            return enableDynamicStopLoss;
        }

        public void setEnableDynamicStopLoss(boolean enableDynamicStopLoss) {
            this.enableDynamicStopLoss = enableDynamicStopLoss;
        }

        // 🔥 新增：维加斯通道追高追低保护配置的getter和setter
        public boolean isEnableVegasAntiChasing() {
            return enableVegasAntiChasing;
        }

        public void setEnableVegasAntiChasing(boolean enableVegasAntiChasing) {
            this.enableVegasAntiChasing = enableVegasAntiChasing;
        }

        public BigDecimal getShortTermRiskThreshold() {
            return shortTermRiskThreshold;
        }

        public void setShortTermRiskThreshold(BigDecimal shortTermRiskThreshold) {
            this.shortTermRiskThreshold = shortTermRiskThreshold;
        }

        public BigDecimal getMediumTermRiskThreshold() {
            return mediumTermRiskThreshold;
        }

        public void setMediumTermRiskThreshold(BigDecimal mediumTermRiskThreshold) {
            this.mediumTermRiskThreshold = mediumTermRiskThreshold;
        }

        public BigDecimal getMinDynamicThreshold() {
            return minDynamicThreshold;
        }

        public void setMinDynamicThreshold(BigDecimal minDynamicThreshold) {
            this.minDynamicThreshold = minDynamicThreshold;
        }

        public BigDecimal getMaxDynamicThreshold() {
            return maxDynamicThreshold;
        }

        public void setMaxDynamicThreshold(BigDecimal maxDynamicThreshold) {
            this.maxDynamicThreshold = maxDynamicThreshold;
        }
    }

    /**
     * 加仓配置
     */
    public static class AddPosition {
        private BigDecimal minAddPositionDistance = BigDecimal.valueOf(0.2); // 最小加仓距离百分比
        private int limitOrderTimeoutMinutes = 5; // 限价单超时时间
        private BigDecimal addPositionUsdt = BigDecimal.valueOf(50); // 新增：每次加仓的USDT金额
        
        // 🔥 新增：反转站稳市价加仓配置
        private boolean enableReversalMarketAdd = true; // 是否启用反转站稳市价加仓
        private BigDecimal reversalLossThreshold = BigDecimal.valueOf(4.0); // 反转加仓亏损门槛（百分比）
        private int reversalMaPeriods = 2; // 反转确认需要的MA连续周期数

        public BigDecimal getMinAddPositionDistance() {
            return minAddPositionDistance;
        }

        public void setMinAddPositionDistance(BigDecimal minAddPositionDistance) {
            this.minAddPositionDistance = minAddPositionDistance;
        }

        public int getLimitOrderTimeoutMinutes() {
            return limitOrderTimeoutMinutes;
        }

        public void setLimitOrderTimeoutMinutes(int limitOrderTimeoutMinutes) {
            this.limitOrderTimeoutMinutes = limitOrderTimeoutMinutes;
        }

        public BigDecimal getAddPositionUsdt() {
            return addPositionUsdt;
        }

        public void setAddPositionUsdt(BigDecimal addPositionUsdt) {
            this.addPositionUsdt = addPositionUsdt;
        }
        
        // 🔥 新增：反转站稳市价加仓配置的getter和setter
        public boolean isEnableReversalMarketAdd() {
            return enableReversalMarketAdd;
        }

        public void setEnableReversalMarketAdd(boolean enableReversalMarketAdd) {
            this.enableReversalMarketAdd = enableReversalMarketAdd;
        }

        public BigDecimal getReversalLossThreshold() {
            return reversalLossThreshold;
        }

        public void setReversalLossThreshold(BigDecimal reversalLossThreshold) {
            this.reversalLossThreshold = reversalLossThreshold;
        }

        public int getReversalMaPeriods() {
            return reversalMaPeriods;
        }

        public void setReversalMaPeriods(int reversalMaPeriods) {
            this.reversalMaPeriods = reversalMaPeriods;
        }
    }

    public DynamicPosition getDynamicPosition() {
        return dynamicPosition;
    }

    public void setDynamicPosition(DynamicPosition dynamicPosition) {
        this.dynamicPosition = dynamicPosition;
    }

    public static class DynamicPosition {
        private BigDecimal basePositionUsdt = new BigDecimal("30.0"); // 基础开仓金额30U
        private BigDecimal maxPositionUsdt = new BigDecimal("200.0"); // 最大单次开仓金额200U
        private BigDecimal profitReinvestRate = new BigDecimal("0.5"); // 利润再投资比例50%
        private int consecutiveWinBonus = 3; // 连续盈利3次后增加仓位
        private BigDecimal winBonusMultiplier = new BigDecimal("1.2"); // 连胜奖励倍数1.2x
        
        // Getters and Setters
        public BigDecimal getBasePositionUsdt() { return basePositionUsdt; }
        public void setBasePositionUsdt(BigDecimal basePositionUsdt) { this.basePositionUsdt = basePositionUsdt; }
        
        public BigDecimal getMaxPositionUsdt() { return maxPositionUsdt; }
        public void setMaxPositionUsdt(BigDecimal maxPositionUsdt) { this.maxPositionUsdt = maxPositionUsdt; }
        
        public BigDecimal getProfitReinvestRate() { return profitReinvestRate; }
        public void setProfitReinvestRate(BigDecimal profitReinvestRate) { this.profitReinvestRate = profitReinvestRate; }
        
        public int getConsecutiveWinBonus() { return consecutiveWinBonus; }
        public void setConsecutiveWinBonus(int consecutiveWinBonus) { this.consecutiveWinBonus = consecutiveWinBonus; }
        
        public BigDecimal getWinBonusMultiplier() { return winBonusMultiplier; }
        public void setWinBonusMultiplier(BigDecimal winBonusMultiplier) { this.winBonusMultiplier = winBonusMultiplier; }
    }
}


