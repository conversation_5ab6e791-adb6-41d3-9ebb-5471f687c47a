package com.trading.bot.scheduler;

import com.trading.bot.config.TradingConfig;
import com.trading.bot.entity.Position;
import com.trading.bot.entity.TradeRecord;
import com.trading.bot.repository.TradeRecordRepository;
import com.trading.bot.risk.RiskManager;
import com.trading.bot.service.PositionSizingService;
import com.trading.bot.service.TradingService;
import com.trading.bot.strategy.AddPositionStrategy;
import com.trading.bot.strategy.MA25TradingStrategy;
import com.trading.bot.analysis.SupportResistanceDetector;
import com.trading.bot.analysis.VegasChannelAnalyzer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 交易策略调度器
 * 定时执行交易策略
 */
@Component
public class TradingScheduler {

    private static final Logger logger = LoggerFactory.getLogger(TradingScheduler.class);

    @Autowired
    private TradingService tradingService;

    @Autowired
    private MA25TradingStrategy ma25TradingStrategy;

    @Autowired
    private AddPositionStrategy addPositionStrategy;

    @Autowired
    private RiskManager riskManager;

    @Autowired
    private TradingConfig tradingConfig;
    
    @Autowired
    private TradeRecordRepository tradeRecordRepository;
    
    @Autowired
    private SupportResistanceDetector supportResistanceDetector;
    
    @Autowired
    private PositionSizingService positionSizingService;
    
    @Autowired
    private VegasChannelAnalyzer vegasChannelAnalyzer;
    
    @Autowired
    private com.trading.bot.service.BinanceApiService binanceApiService;

    @Autowired
    private com.trading.bot.repository.PositionRepository positionRepository;

    // 🔥 新增：对冲操作冷却缓存，防止频繁操作
    private final java.util.concurrent.ConcurrentHashMap<String, LocalDateTime> hedgeOperationCooldown = new java.util.concurrent.ConcurrentHashMap<>();
    
    /**
     * 🔥 新增：支撑阻力位检测缓存，避免频繁计算
     */
    private final java.util.concurrent.ConcurrentHashMap<String, LocalDateTime> lastLevelCheckTime = new java.util.concurrent.ConcurrentHashMap<>();

    /**
     * 每分钟执行一次，检查交易信号并执行交易
     */
    @Scheduled(cron = "0 * * * * ?") // 每分钟执行一次
    public void executeTradingStrategy() {
        logger.info("====== 开始执行交易策略，当前时间: {} ======", LocalDateTime.now());

        for (String symbol : tradingConfig.getSymbols()) {
            logger.info("--- 检查交易对: {} ---", symbol);

            // 1. 检查开仓信号
            boolean hasOpenedPosition = checkAndExecuteOpenPosition(symbol);

            // 🔥 关键修复：如果刚开仓成功，跳过本次加仓检查，避免同一周期内重复操作
            if (!hasOpenedPosition) {
                // 2. 只有在没有开仓的情况下才检查加仓信号
                checkAndExecuteAddPosition(symbol);
            } else {
                logger.info("[{}] 本周期刚开仓成功，跳过加仓检查，等待下个周期", symbol);
            }
        }

        logger.info("====== 交易策略执行完毕 ======");
    }

    /**
     * 每5分钟检查一次限价单状态
     */
    @Scheduled(cron = "0 */5 * * * ?") // 每5分钟执行一次
    public void checkLimitOrderStatus() {
        logger.debug("====== 开始检查限价单状态，当前时间: {} ======", LocalDateTime.now());

        for (String symbol : tradingConfig.getSymbols()) {
            checkSymbolLimitOrders(symbol);
        }

        logger.debug("====== 限价单状态检查完毕 ======");
    }

    /**
     * 每分钟执行一次：监控止盈止损 + 同步币安仓位
     */
    @Scheduled(fixedRate = 60000) // 1分钟
    public void monitorProfitAndLoss() {
        logger.debug("开始监控止盈止损和同步仓位...");
        
        List<Position> activePositions = tradingService.getAllActivePositions();
        
        // 🔥 修复：按交易对分组，避免重复处理对冲策略
        Map<String, List<Position>> positionsBySymbol = activePositions.stream()
            .collect(java.util.stream.Collectors.groupingBy(Position::getSymbol));
        
        for (Map.Entry<String, List<Position>> entry : positionsBySymbol.entrySet()) {
            String symbol = entry.getKey();
            List<Position> symbolPositions = entry.getValue();
            
            try {
                // 🔥 关键修复：每个交易对只检查一次对冲策略
                boolean hasProcessedHedge = false;
                
                // 1. 首先统一处理对冲策略（如果有多空双持）
                if (hasHedgePositions(symbol)) {
                    logger.debug("[{}] 检测到对冲持仓，执行对冲策略", symbol);
                    boolean hedgeProcessed = handleFlexibleHedgeStrategy(symbol);
                    if (hedgeProcessed) {
                        hasProcessedHedge = true;
                        logger.debug("[{}] 对冲策略已处理，跳过单独持仓检查", symbol);
                        continue; // 对冲策略处理后，跳过单独持仓检查
                    }
                }
                
                // 2. 如果没有处理对冲，则逐个处理单独持仓
                if (!hasProcessedHedge) {
                    for (Position position : symbolPositions) {
                        try {
                            // 同步币安仓位数据，确保本地数据准确
                            boolean syncSuccess = tradingService.syncPositionFromBinance(symbol, position.getPositionSide());
                            if (!syncSuccess) {
                                logger.warn("[{}] 同步币安{}仓位失败，跳过此次检查", symbol, position.getPositionSide());
                                continue;
                            }
                            
                            // 重新获取同步后的仓位数据
                            Optional<Position> updatedPositionOpt = tradingService.getActivePosition(symbol, position.getPositionSide());
                            if (!updatedPositionOpt.isPresent()) {
                                logger.warn("[{}] 同步后未找到活跃{}仓位，可能已被平仓", symbol, position.getPositionSide());
                                continue;
                            }
                            Position updatedPosition = updatedPositionOpt.get();
                            
                            // 获取当前价格
                            BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
                            if (currentPrice.compareTo(BigDecimal.ZERO) <= 0) {
                                logger.error("[{}] 无法获取当前价格，跳过{}持仓止盈止损检查", symbol, position.getPositionSide());
                                continue;
                            }
                            
                            // 检查硬止损：只保留总亏损限制
                            BigDecimal unrealizedPnl = updatedPosition.calculatePnl(currentPrice);
                            
                            // 🔥 简化止损逻辑：只保留总亏损限制，完全去掉15%动态止损
                            BigDecimal maxLossUsdt = tradingConfig.getRisk().getMaxLossUsdt(); // 配置80U
                            if (unrealizedPnl.compareTo(maxLossUsdt.negate()) <= 0) {
                                BigDecimal lossPercentage = unrealizedPnl.abs().divide(updatedPosition.getTotalAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                                logger.warn("[{}] {}仓位亏损{}U达到总亏损限制{}U({}%)，强制平仓 | 总投入: {}U", 
                                           symbol, updatedPosition.getPositionSide(), unrealizedPnl.abs(), maxLossUsdt, 
                                           lossPercentage.toPlainString(), updatedPosition.getTotalAmount());
                                
                                String side = "LONG".equalsIgnoreCase(updatedPosition.getPositionSide()) ? "SELL" : "BUY";
                                tradingService.closePosition(symbol, side, BigDecimal.ONE, "MAX_LOSS_STOP");
                                continue;
                            }
                            
                            // 检查单个持仓的止盈策略
                            if ("LONG".equalsIgnoreCase(updatedPosition.getPositionSide())) {
                                handleLongProfitStrategy(symbol, updatedPosition);
                            } else if ("SHORT".equalsIgnoreCase(updatedPosition.getPositionSide())) {
                                handleShortProfitStrategy(symbol, updatedPosition);
                            }
                            
                        } catch (Exception e) {
                            logger.error("[{}] 监控{}持仓止盈止损异常: {}", symbol, position.getPositionSide(), e.getMessage());
                        }
                    }
                }
                
            } catch (Exception e) {
                logger.error("[{}] 监控交易对止盈止损异常: {}", symbol, e.getMessage());
            }
        }
    }
    
    /**
     * 处理多单止盈策略
     */
    private void handleLongProfitStrategy(String symbol, Position position) {
        String profitSignal = ma25TradingStrategy.checkLongProfitSignal(symbol, position);
        if (profitSignal != null) {
            BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
            BigDecimal pnl = position.calculatePnl(currentPrice);
            
            // 🔥 修复：检查是否有未成交的止盈限价单，避免冲突
            List<TradeRecord> pendingProfitOrders = tradingService.getPendingProfitOrders(symbol, "LONG");
            if (!pendingProfitOrders.isEmpty() && !"RESISTANCE_LIMIT_HANDLED".equals(profitSignal)) {
                logger.info("[{}] 💰 多单已有{}个止盈限价单待成交，跳过市价止盈避免冲突", symbol, pendingProfitOrders.size());
                return;
            }
            
            if ("CLOSE_ADDED".equals(profitSignal)) {
                // 平掉加仓部分，保留底仓
                logger.info("[{}] 💰 多单平掉加仓部分，保留底仓", symbol);
                boolean closeSuccess = tradingService.closeAddedPositionOnly(symbol, position.getPositionSide(), "CLOSE_ADDED_LONG");
                if (closeSuccess) {
                    // 🔥 修复：平掉加仓部分不设置hasProfitReduced标记，允许后续加仓减仓循环
                    // position.setHasProfitReduced(true); // 注释掉，允许后续加仓减仓
                    positionRepository.save(position);
                    logger.info("[{}] 💰 多单平掉加仓部分成功，保留后续加仓减仓能力", symbol);
                }
                positionSizingService.recordTradeProfit(symbol, pnl, "CLOSE_ADDED_LONG");
                
            } else if ("FULL_CLOSE".equals(profitSignal)) {
                // 完全平仓
                logger.info("[{}] 💰 多单完全平仓", symbol);
                tradingService.closePosition(symbol, "SELL", BigDecimal.ONE, "FULL_CLOSE");
                positionSizingService.recordTradeProfit(symbol, pnl, "FULL_CLOSE_LONG");
                
                // 🔥 生生不息：检查是否快速重新开仓
                scheduleQuickReopenCheck(symbol, "LONG", currentPrice);
                
            } else if ("MA25_REVERSAL".equals(profitSignal)) {
                // MA25反转止盈：保留20U底仓
                logger.info("[{}] 💰 多单MA25反转止盈，保留20U底仓", symbol);
                boolean closeSuccess = tradingService.closePositionAndRetainBase(symbol, position.getPositionSide(), new BigDecimal("20"), "MA25_REVERSAL_PROFIT");
                if (closeSuccess) {
                    // 🔥 修复：只在减仓成功后才设置标记
                    position.setHasProfitReduced(true);
                    positionRepository.save(position);
                    logger.info("[{}] 💰 多单MA25反转止盈成功，已设置减仓标记", symbol);
                }
                positionSizingService.recordTradeProfit(symbol, pnl, "MA25_REVERSAL_LONG");
                
                // 🔥 生生不息：检查是否快速重新开仓
                scheduleQuickReopenCheck(symbol, "LONG", currentPrice);
                
            } else if ("RESISTANCE_REDUCE".equals(profitSignal)) {
                // 🔥 修复：优先检查是否已经减仓过，确保"只减仓一次"逻辑
                if (position.isHasProfitReduced()) {
                    logger.debug("[{}] 多单已进行过盈利减仓，跳过阻力位减仓，等待MA25反转信号", symbol);
                } else {
                    // 🔥 新增：检查阻力位减仓冷却期，防止连续减仓
                    if (!canExecuteHedgeOperation(symbol, "LONG_RESISTANCE", 3)) {
                        logger.debug("[{}] 多单阻力位减仓冷却中，跳过", symbol);
                    } else {
                        // 阻力位减仓50%（市价单回退逻辑）
                        logger.info("[{}] 💰 多单接近阻力位，减仓50%", symbol);
                        boolean closeSuccess = tradingService.closePosition(symbol, "SELL", new BigDecimal("0.5"), "RESISTANCE_REDUCE");
                        if (closeSuccess) {
                            // 🔥 修复：只在减仓成功后才设置标记
                            position.setHasProfitReduced(true);
                            positionRepository.save(position);
                            logger.info("[{}] 💰 多单阻力位减仓成功，已设置减仓标记", symbol);
                            
                            // 🔥 新增：记录减仓操作时间，防止连续减仓
                            recordHedgeOperation(symbol, "LONG_RESISTANCE");
                        }
                        BigDecimal partialPnl = pnl.multiply(new BigDecimal("0.5"));
                        positionSizingService.recordTradeProfit(symbol, partialPnl, "RESISTANCE_REDUCE_LONG");
                    }
                }
                
            } else if ("RESISTANCE_LIMIT_HANDLED".equals(profitSignal)) {
                // 阻力位限价减仓已处理，无需额外操作
                logger.info("[{}] 💰 多单阻力位限价减仓已挂单，等待成交，跳过市价止盈", symbol);
            }
            
            // 🔥 新增：检查小额持仓完全平仓（单独持仓逻辑）
            if (position.getTotalAmount().compareTo(new BigDecimal("5.0")) < 0 && pnl.compareTo(BigDecimal.ZERO) > 0) {
                // 🔥 新增：检查是否已经减仓过，避免重复操作
                if (!position.isHasProfitReduced()) {
                    logger.info("[{}] 🎯 LONG_SMALL_POSITION_CLOSE - 多单小额持仓{}U，盈利{}U，完全平仓", 
                               symbol, position.getTotalAmount().toPlainString(), pnl.toPlainString());
                    boolean closeSuccess = tradingService.closePosition(symbol, "SELL", BigDecimal.ONE, "LONG_SMALL_POSITION_CLOSE");
                    if (closeSuccess) {
                        // 🔥 修复：小额持仓完全平仓不设置hasProfitReduced标记，因为这是完全平仓
                        // position.setHasProfitReduced(true); // 注释掉，完全平仓不需要设置标记
                        positionRepository.save(position);
                        logger.info("[{}] 🎯 多单小额持仓完全平仓成功", symbol);
                    }
                    positionSizingService.recordTradeProfit(symbol, pnl, "LONG_SMALL_POSITION_CLOSE");
                } else {
                    logger.debug("[{}] 多单小额持仓已减仓过，跳过完全平仓", symbol);
                }
            }
        }
    }
    
    /**
     * 处理空单止盈策略
     */
    private void handleShortProfitStrategy(String symbol, Position position) {
        String profitSignal = ma25TradingStrategy.checkShortProfitSignal(symbol, position);
        if (profitSignal != null) {
            BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
            BigDecimal pnl = position.calculatePnl(currentPrice);
            
            // 🔥 修复：检查是否有未成交的止盈限价单，避免冲突
            List<TradeRecord> pendingProfitOrders = tradingService.getPendingProfitOrders(symbol, "SHORT");
            if (!pendingProfitOrders.isEmpty() && !"SUPPORT_LIMIT_HANDLED".equals(profitSignal)) {
                logger.info("[{}] 💰 空单已有{}个止盈限价单待成交，跳过市价止盈避免冲突", symbol, pendingProfitOrders.size());
                return;
            }
            
            if ("CLOSE_ADDED".equals(profitSignal)) {
                // 平掉加仓部分，保留底仓
                logger.info("[{}] 💰 空单平掉加仓部分，保留底仓", symbol);
                boolean closeSuccess = tradingService.closeAddedPositionOnly(symbol, position.getPositionSide(), "CLOSE_ADDED_SHORT");
                if (closeSuccess) {
                    // 🔥 修复：平掉加仓部分不设置hasProfitReduced标记，允许后续加仓减仓循环
                    // position.setHasProfitReduced(true); // 注释掉，允许后续加仓减仓
                    positionRepository.save(position);
                    logger.info("[{}] 💰 空单平掉加仓部分成功，保留后续加仓减仓能力", symbol);
                }
                positionSizingService.recordTradeProfit(symbol, pnl, "CLOSE_ADDED_SHORT");
                
            } else if ("FULL_CLOSE".equals(profitSignal)) {
                // 完全平仓
                logger.info("[{}] 💰 空单完全平仓", symbol);
                tradingService.closePosition(symbol, "BUY", BigDecimal.ONE, "FULL_CLOSE");
                positionSizingService.recordTradeProfit(symbol, pnl, "FULL_CLOSE_SHORT");
                
                // 🔥 生生不息：检查是否快速重新开仓
                scheduleQuickReopenCheck(symbol, "SHORT", currentPrice);
                
            } else if ("MA25_REVERSAL".equals(profitSignal)) {
                // MA25反转止盈：保留20U底仓
                logger.info("[{}] 💰 空单MA25反转止盈，保留20U底仓", symbol);
                boolean closeSuccess = tradingService.closePositionAndRetainBase(symbol, position.getPositionSide(), new BigDecimal("20"), "MA25_REVERSAL_PROFIT");
                if (closeSuccess) {
                    // 🔥 修复：只在减仓成功后才设置标记
                    position.setHasProfitReduced(true);
                    positionRepository.save(position);
                    logger.info("[{}] 💰 空单MA25反转止盈成功，已设置减仓标记", symbol);
                }
                positionSizingService.recordTradeProfit(symbol, pnl, "MA25_REVERSAL_SHORT");
                
                // 🔥 生生不息：检查是否快速重新开仓
                scheduleQuickReopenCheck(symbol, "SHORT", currentPrice);
                
            } else if ("SUPPORT_REDUCE".equals(profitSignal)) {
                // 🔥 修复：优先检查是否已经减仓过，确保"只减仓一次"逻辑
                if (position.isHasProfitReduced()) {
                    logger.debug("[{}] 空单已进行过盈利减仓，跳过支撑位减仓，等待MA25反转信号", symbol);
                } else {
                    // 🔥 新增：检查支撑位减仓冷却期，防止连续减仓
                    if (!canExecuteHedgeOperation(symbol, "SHORT_SUPPORT", 3)) {
                        logger.debug("[{}] 空单支撑位减仓冷却中，跳过", symbol);
                    } else {
                        // 支撑位减仓50%（市价单回退逻辑）
                        logger.info("[{}] 💰 空单接近支撑位，减仓50%", symbol);
                        boolean closeSuccess = tradingService.closePosition(symbol, "BUY", new BigDecimal("0.5"), "SUPPORT_REDUCE");
                        if (closeSuccess) {
                            // 🔥 修复：只在减仓成功后才设置标记
                            position.setHasProfitReduced(true);
                            positionRepository.save(position);
                            logger.info("[{}] 💰 空单支撑位减仓成功，已设置减仓标记", symbol);
                            
                            // 🔥 新增：记录减仓操作时间，防止连续减仓
                            recordHedgeOperation(symbol, "SHORT_SUPPORT");
                        }
                        BigDecimal partialPnl = pnl.multiply(new BigDecimal("0.5"));
                        positionSizingService.recordTradeProfit(symbol, partialPnl, "SUPPORT_REDUCE_SHORT");
                    }
                }
                
            } else if ("SUPPORT_LIMIT_HANDLED".equals(profitSignal)) {
                // 支撑位限价减仓已处理，无需额外操作
                logger.info("[{}] 💰 空单支撑位限价减仓已挂单，等待成交，跳过市价止盈", symbol);
            }
            
            // 🔥 新增：检查小额持仓完全平仓（单独持仓逻辑）
            if (position.getTotalAmount().compareTo(new BigDecimal("5.0")) < 0 && pnl.compareTo(BigDecimal.ZERO) > 0) {
                // 🔥 新增：检查是否已经减仓过，避免重复操作
                if (!position.isHasProfitReduced()) {
                    logger.info("[{}] 🎯 SHORT_SMALL_POSITION_CLOSE - 空单小额持仓{}U，盈利{}U，完全平仓", 
                               symbol, position.getTotalAmount().toPlainString(), pnl.toPlainString());
                    boolean closeSuccess = tradingService.closePosition(symbol, "BUY", BigDecimal.ONE, "SHORT_SMALL_POSITION_CLOSE");
                    if (closeSuccess) {
                        // 🔥 修复：小额持仓完全平仓不设置hasProfitReduced标记，因为这是完全平仓
                        // position.setHasProfitReduced(true); // 注释掉，完全平仓不需要设置标记
                        positionRepository.save(position);
                        logger.info("[{}] 🎯 空单小额持仓完全平仓成功", symbol);
                    }
                    positionSizingService.recordTradeProfit(symbol, pnl, "SHORT_SMALL_POSITION_CLOSE");
                } else {
                    logger.debug("[{}] 空单小额持仓已减仓过，跳过完全平仓", symbol);
                }
            }
        }
    }

    private boolean checkAndExecuteOpenPosition(String symbol) {
        logger.info("[{}] STRATEGY_ANALYSIS_START - 开始策略分析", symbol);
        
        // 1. 获取当前价格
        BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
        if (currentPrice.compareTo(BigDecimal.ZERO) <= 0) {
            logger.error("[{}] 无法获取当前价格，跳过策略分析", symbol);
            return false;
        }
        logger.info("[{}] CURRENT_PRICE - 当前价格: {}", symbol, currentPrice.toPlainString());
        
        // 2. 检查账户余额（基础风控）
        BigDecimal accountBalance = tradingService.getAccountBalance();
        BigDecimal minRequiredBalance = tradingConfig.getInitialPositionUsdt().multiply(BigDecimal.valueOf(2)); // 至少2倍保证金
        if (accountBalance.compareTo(minRequiredBalance) < 0) {
            logger.warn("[{}] 账户余额{}U不足，需要至少{}U，跳过开仓", symbol, accountBalance, minRequiredBalance);
            return false;
        }
        
        // 3. 同步币安真实仓位状态（智能同步：仅在本地无记录时同步）
        Optional<Position> localLongPosition = tradingService.getActivePosition(symbol, "LONG");
        Optional<Position> localShortPosition = tradingService.getActivePosition(symbol, "SHORT");
        
        if (!localLongPosition.isPresent()) {
            logger.debug("[{}] 本地无多单记录，同步币安多单状态", symbol);
            tradingService.syncPositionFromBinance(symbol, "LONG");
        }
        if (!localShortPosition.isPresent()) {
            logger.debug("[{}] 本地无空单记录，同步币安空单状态", symbol);
            tradingService.syncPositionFromBinance(symbol, "SHORT");
        }
        
        // 4. 获取最终的持仓状态
        Optional<Position> longPosition = tradingService.getActivePosition(symbol, "LONG");
        Optional<Position> shortPosition = tradingService.getActivePosition(symbol, "SHORT");
        
        String positionStatus = "";
        if (longPosition.isPresent() && shortPosition.isPresent()) {
            positionStatus = "多空双持";
        } else if (longPosition.isPresent()) {
            positionStatus = "仅持多单";
        } else if (shortPosition.isPresent()) {
            positionStatus = "仅持空单";  
        } else {
            positionStatus = "无持仓";
        }
        logger.info("[{}] POSITION_STATUS - 持仓状态: {}", symbol, positionStatus);

        // 📊 详细持仓状态分析
        logDetailedPositionAnalysis(symbol, longPosition, shortPosition, currentPrice);

        // 🔥 新增：维加斯通道大趋势分析
        logSimplifiedMarketAnalysis(symbol, currentPrice);

        // 🔥 修复：总是检查开多信号（输出详细MA25分析），但只在无多单时执行开仓
        logger.info("[{}] CHECKING_LONG_SIGNAL - 检查开多信号", symbol);
        boolean longSignal = ma25TradingStrategy.checkLongOpenSignal(symbol);
        
        if (longSignal) {
            if (!longPosition.isPresent()) {
                // 有开多信号且无多单持仓，执行开多逻辑
                logger.info("[{}] LONG_SIGNAL_DETECTED - 检测到开多信号，准备执行开仓", symbol);
                
                // 检测到开多信号时输出支撑阻力位信息
                smartCheckSupportResistanceLevels(symbol, currentPrice, "开多信号检测");
                
                // 检查开仓冷却期
                if (ma25TradingStrategy.canOpenPosition(symbol, "LONG")) {
                    logger.info("[{}] EXECUTING_LONG_OPEN - 执行开多操作", symbol);
                    
                    // 🔥 智能对冲机制：根据是否有反向持仓决定开仓金额（简化风险控制）
                    BigDecimal openPositionSize;
                    String openReason;
                    
                    if (shortPosition.isPresent()) {
                        // 有空单持仓：固定30U对冲开多，避免等额锁死
                        Position shortPos = shortPosition.get();
                        BigDecimal shortPositionValue = shortPos.getQuantity().multiply(currentPrice);
                        
                        // 🔥 修复：固定30U对冲开仓，避免两边锁死
                        openPositionSize = tradingConfig.getDynamicPosition().getBasePositionUsdt(); // 固定30U
                        openReason = "MA25_LONG_HEDGE_FIXED"; // 固定对冲开仓
                        
                        // 🔥 调试日志：确认变量值
                        logger.info("[{}] 🔍 变量检查：shortPositionValue={}U, basePositionUsdt={}U, openPositionSize={}U", 
                                   symbol, shortPositionValue.toPlainString(), 
                                   tradingConfig.getDynamicPosition().getBasePositionUsdt().toPlainString(),
                                   openPositionSize.toPlainString());
                        
                        logger.info("[{}] 🔄 固定对冲开多：空单价值{}U，固定对冲开仓{}U（避免锁死）", 
                                   symbol, shortPositionValue.toPlainString(), openPositionSize.toPlainString());
                    } else {
                        // 无持仓：使用固定底仓金额
                        openPositionSize = tradingConfig.getDynamicPosition().getBasePositionUsdt(); // 固定30U
                        openReason = "MA25_LONG_OPEN_SIMPLE"; // 常规开仓
                        
                        logger.info("[{}] 💰 常规开多：无持仓状态，固定底仓{}U", 
                                   symbol, openPositionSize.toPlainString());
                    }
                    
                    // 🔥 新的风险控制：多空合计总仓位不超过1000U
                    BigDecimal finalOpenSize = checkAndAdjustPositionSize(symbol, currentPrice, openPositionSize, "开多");
                    
                    // 🔥 调试日志：确认风控后的值
                    logger.info("[{}] 🔍 风控返回值检查：原计划={}U, 风控后={}U", 
                               symbol, openPositionSize.toPlainString(), finalOpenSize.toPlainString());
                    
                    if (finalOpenSize.compareTo(BigDecimal.ZERO) <= 0) {
                        logger.warn("[{}] ❌ 开多被风控拒绝：总仓位已达限制", symbol);
                        return false;
                    }
                    
                    BigDecimal quantity = finalOpenSize.divide(currentPrice, tradingService.getQuantityPrecision(symbol), RoundingMode.HALF_UP);
                    
                    // 🔥 调试日志：检查数量计算是否正确
                    BigDecimal verifyAmount = quantity.multiply(currentPrice);
                    logger.info("[{}] 🔍 开多数量计算验证：finalOpenSize={}U, currentPrice={}, quantity={}, 验证金额={}U", 
                               symbol, finalOpenSize.toPlainString(), currentPrice.toPlainString(), 
                               quantity.toPlainString(), verifyAmount.toPlainString());
                    
                    boolean success = tradingService.openPosition(symbol, "BUY", quantity, "LONG", openReason);
                    if (success) {
                        if (shortPosition.isPresent()) {
                            logger.info("[{}] 🎉 LONG_HEDGE_SUCCESS - 对冲开多成功，实际开仓{}U", symbol, finalOpenSize.toPlainString());
                        } else {
                            logger.info("[{}] 🎉 LONG_OPEN_SUCCESS - 常规开多成功，实际开仓{}U", symbol, finalOpenSize.toPlainString());
                        }
                        return true;
                    } else {
                        logger.warn("[{}] ❌ LONG_OPEN_FAILED - 开多操作失败", symbol);
                        return false;
                    }
                } else {
                    logger.info("[{}] LONG_COOLDOWN - 开多信号已检测到，但处于冷却期", symbol);
                    return false;
                }
            } else {
                logger.info("[{}] LONG_SIGNAL_WITH_POSITION - 检测到开多信号，但已有多单持仓，跳过开仓", symbol);
            }
        } else {
            if (longPosition.isPresent()) {
                logger.info("[{}] NO_LONG_SIGNAL_WITH_POSITION - 无开多信号，已有多单持仓", symbol);
            } else {
                logger.info("[{}] NO_LONG_SIGNAL - 无开多信号", symbol);
            }
        }

        // 🔥 修复：总是检查开空信号（输出详细MA25分析），但只在无空单时执行开仓
        logger.info("[{}] CHECKING_SHORT_SIGNAL - 检查开空信号", symbol);
        boolean shortSignal = ma25TradingStrategy.checkShortOpenSignal(symbol);
        
        if (shortSignal) {
            if (!shortPosition.isPresent()) {
                // 检测到开空信号时输出支撑阻力位信息
                smartCheckSupportResistanceLevels(symbol, currentPrice, "开空信号检测");
                
                // 检查开仓冷却期
                if (ma25TradingStrategy.canOpenPosition(symbol, "SHORT")) {
                    logger.info("[{}] EXECUTING_SHORT_OPEN - 执行开空操作", symbol);
                    
                    // 🔥 智能对冲机制：根据是否有反向持仓决定开仓金额（简化风险控制）
                    BigDecimal openPositionSize;
                    String openReason;
                    
                    if (longPosition.isPresent()) {
                        // 有多单持仓：固定30U对冲开空，避免等额锁死
                        Position longPos = longPosition.get();
                        BigDecimal longPositionValue = longPos.getQuantity().multiply(currentPrice);
                        
                        // 🔥 修复：固定30U对冲开仓，避免两边锁死
                        openPositionSize = tradingConfig.getDynamicPosition().getBasePositionUsdt(); // 固定30U
                        openReason = "MA25_SHORT_HEDGE_FIXED"; // 固定对冲开仓
                        
                        // 🔥 调试日志：确认变量值
                        logger.info("[{}] 🔍 变量检查：longPositionValue={}U, basePositionUsdt={}U, openPositionSize={}U", 
                                   symbol, longPositionValue.toPlainString(), 
                                   tradingConfig.getDynamicPosition().getBasePositionUsdt().toPlainString(),
                                   openPositionSize.toPlainString());
                        
                        logger.info("[{}] 🔄 固定对冲开空：多单价值{}U，固定对冲开仓{}U（避免锁死）", 
                                   symbol, longPositionValue.toPlainString(), openPositionSize.toPlainString());
                    } else {
                        // 无持仓：使用固定底仓金额
                        openPositionSize = tradingConfig.getDynamicPosition().getBasePositionUsdt(); // 固定30U
                        openReason = "MA25_SHORT_OPEN_SIMPLE"; // 常规开仓
                        
                        logger.info("[{}] 💰 常规开空：无持仓状态，固定底仓{}U", 
                                   symbol, openPositionSize.toPlainString());
                    }
                    
                    // 🔥 新的风险控制：多空合计总仓位不超过1000U
                    BigDecimal finalOpenSize = checkAndAdjustPositionSize(symbol, currentPrice, openPositionSize, "开空");
                    
                    // 🔥 调试日志：确认风控后的值
                    logger.info("[{}] 🔍 风控返回值检查：原计划={}U, 风控后={}U", 
                               symbol, openPositionSize.toPlainString(), finalOpenSize.toPlainString());
                    
                    if (finalOpenSize.compareTo(BigDecimal.ZERO) <= 0) {
                        logger.warn("[{}] ❌ 开空被风控拒绝：总仓位已达限制", symbol);
                        return false;
                    }
                    
                    BigDecimal quantity = finalOpenSize.divide(currentPrice, tradingService.getQuantityPrecision(symbol), RoundingMode.HALF_UP);
                    
                    // 🔥 调试日志：检查数量计算是否正确
                    BigDecimal verifyAmount = quantity.multiply(currentPrice);
                    logger.info("[{}] 🔍 开空数量计算验证：finalOpenSize={}U, currentPrice={}, quantity={}, 验证金额={}U", 
                               symbol, finalOpenSize.toPlainString(), currentPrice.toPlainString(), 
                               quantity.toPlainString(), verifyAmount.toPlainString());
                    
                    boolean success = tradingService.openPosition(symbol, "SELL", quantity, "SHORT", openReason);
                    if (success) {
                        if (longPosition.isPresent()) {
                            logger.info("[{}] 🎉 SHORT_HEDGE_SUCCESS - 对冲开空成功，实际开仓{}U", symbol, finalOpenSize.toPlainString());
                        } else {
                            logger.info("[{}] 🎉 SHORT_OPEN_SUCCESS - 常规开空成功，实际开仓{}U", symbol, finalOpenSize.toPlainString());
                        }
                        return true;
                    } else {
                        logger.warn("[{}] ❌ SHORT_OPEN_FAILED - 开空操作失败", symbol);
                        return false;
                    }
                } else {
                    logger.info("[{}] SHORT_COOLDOWN - 开空信号已检测到，但处于冷却期", symbol);
                    return false;
                }
            } else {
                logger.info("[{}] SHORT_SIGNAL_WITH_POSITION - 检测到开空信号，但已有空单持仓，跳过开仓", symbol);
            }
        } else {
            if (shortPosition.isPresent()) {
                logger.info("[{}] NO_SHORT_SIGNAL_WITH_POSITION - 无开空信号，已有空单持仓", symbol);
            } else {
                logger.info("[{}] NO_SHORT_SIGNAL - 无开空信号", symbol);
            }
        }
        
        logger.info("[{}] STRATEGY_ANALYSIS_END - 策略分析完成", symbol);
        return false;
    }

    /**
     * 📊 详细持仓状态分析和日志输出
     */
    private void logDetailedPositionAnalysis(String symbol, Optional<Position> longPosition, Optional<Position> shortPosition, BigDecimal currentPrice) {
        try {
            // 多单持仓详细分析
            if (longPosition.isPresent()) {
                Position longPos = longPosition.get();
                BigDecimal longPnl = longPos.calculatePnl(currentPrice);
                BigDecimal longPnlPercentage = BigDecimal.ZERO;
                if (longPos.getTotalAmount() != null && longPos.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                    longPnlPercentage = longPnl.divide(longPos.getTotalAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                }
                String longPnlSign = longPnl.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "";
                
                // 计算价格偏离度
                BigDecimal longPriceDeviation = currentPrice.subtract(longPos.getAvgPrice()).divide(longPos.getAvgPrice(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                String longDeviationSign = longPriceDeviation.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "";
                
                // 计算仓位价值
                BigDecimal longPositionValue = longPos.getQuantity().multiply(currentPrice);
                
                // 检查止盈条件
                String longProfitSignal = ma25TradingStrategy.checkLongProfitSignal(symbol, longPos);
                boolean canReduceAtResistance = canReducePosition(longPos, currentPrice, "LONG");
                
                logger.info("[{}] 📈 LONG_POSITION_ANALYSIS - 多单详细分析:", symbol);
                logger.info("[{}]    ├─ 基础信息: 数量={}, 均价={}, 仓位价值={}U", 
                           symbol, longPos.getQuantity().toPlainString(), longPos.getAvgPrice().toPlainString(), longPositionValue.toPlainString());
                logger.info("[{}]    ├─ 盈亏状况: {}{}U ({}%) | 价格偏离: {}{}%", 
                           symbol, longPnlSign, longPnl.toPlainString(), longPnlPercentage.toPlainString(), 
                           longDeviationSign, longPriceDeviation.toPlainString());
                logger.info("[{}]    ├─ 仓位结构: 底仓={}U, 加仓={}U, 加仓次数={}", 
                           symbol, 
                           longPos.getBaseAmount() != null ? longPos.getBaseAmount().toPlainString() : "N/A",
                           longPos.getAddedAmount() != null ? longPos.getAddedAmount().toPlainString() : "0",
                           longPos.getAddCount() != null ? longPos.getAddCount() : 0);
                logger.info("[{}]    ├─ 止盈检测: 信号={} | 可减仓={} | 减仓次数={}", 
                           symbol, longProfitSignal != null ? longProfitSignal : "无", canReduceAtResistance, 
                           longPos.getReduceCount() != null ? longPos.getReduceCount() : 0);
                logger.info("[{}]    └─ 风控状态: 持仓时长={}小时", 
                           symbol, longPos.getCreatedAt() != null ? 
                           java.time.Duration.between(longPos.getCreatedAt(), java.time.LocalDateTime.now()).toHours() : "N/A");
            }
            
            // 空单持仓详细分析
            if (shortPosition.isPresent()) {
                Position shortPos = shortPosition.get();
                BigDecimal shortPnl = shortPos.calculatePnl(currentPrice);
                BigDecimal shortPnlPercentage = BigDecimal.ZERO;
                if (shortPos.getTotalAmount() != null && shortPos.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                    shortPnlPercentage = shortPnl.divide(shortPos.getTotalAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                }
                String shortPnlSign = shortPnl.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "";
                
                // 计算价格偏离度
                BigDecimal shortPriceDeviation = shortPos.getAvgPrice().subtract(currentPrice).divide(shortPos.getAvgPrice(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                String shortDeviationSign = shortPriceDeviation.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "";
                
                // 计算仓位价值
                BigDecimal shortPositionValue = shortPos.getQuantity().multiply(currentPrice);
                
                // 检查止盈条件
                String shortProfitSignal = ma25TradingStrategy.checkShortProfitSignal(symbol, shortPos);
                boolean canReduceAtSupport = canReducePosition(shortPos, currentPrice, "SHORT");
                
                logger.info("[{}] 📉 SHORT_POSITION_ANALYSIS - 空单详细分析:", symbol);
                logger.info("[{}]    ├─ 基础信息: 数量={}, 均价={}, 仓位价值={}U", 
                           symbol, shortPos.getQuantity().toPlainString(), shortPos.getAvgPrice().toPlainString(), shortPositionValue.toPlainString());
                logger.info("[{}]    ├─ 盈亏状况: {}{}U ({}%) | 价格偏离: {}{}%", 
                           symbol, shortPnlSign, shortPnl.toPlainString(), shortPnlPercentage.toPlainString(), 
                           shortDeviationSign, shortPriceDeviation.toPlainString());
                logger.info("[{}]    ├─ 仓位结构: 底仓={}U, 加仓={}U, 加仓次数={}", 
                           symbol, 
                           shortPos.getBaseAmount() != null ? shortPos.getBaseAmount().toPlainString() : "N/A",
                           shortPos.getAddedAmount() != null ? shortPos.getAddedAmount().toPlainString() : "0",
                           shortPos.getAddCount() != null ? shortPos.getAddCount() : 0);
                logger.info("[{}]    ├─ 止盈检测: 信号={} | 可减仓={} | 减仓次数={}", 
                           symbol, shortProfitSignal != null ? shortProfitSignal : "无", canReduceAtSupport, 
                           shortPos.getReduceCount() != null ? shortPos.getReduceCount() : 0);
                logger.info("[{}]    └─ 风控状态: 持仓时长={}小时", 
                           symbol, shortPos.getCreatedAt() != null ? 
                           java.time.Duration.between(shortPos.getCreatedAt(), java.time.LocalDateTime.now()).toHours() : "N/A");
            }
            
        } catch (Exception e) {
            logger.error("[{}] 详细持仓分析异常: {}", symbol, e.getMessage());
        }
    }
    
    /**
     * 检查是否可以减仓（简化版本，用于日志显示）
     */
    private boolean canReducePosition(Position position, BigDecimal currentPrice, String positionSide) {
        try {
            // 1. 检查仓位价值门槛
            BigDecimal currentPositionValue = position.getQuantity().multiply(currentPrice);
            BigDecimal minValueThreshold = "LONG".equals(positionSide) ? new BigDecimal("25") : new BigDecimal("15");
            
            if (currentPositionValue.compareTo(minValueThreshold) <= 0) {
                return false;
            }
            
            // 2. 检查减仓次数
            Integer reduceCount = position.getReduceCount();
            if (reduceCount != null && reduceCount >= 2) {
                return false;
            }
            
            // 3. 检查减仓时间间隔
            if (position.getLastReduceTime() != null) {
                long hoursSinceLastReduce = java.time.Duration.between(position.getLastReduceTime(), java.time.LocalDateTime.now()).toHours();
                if (hoursSinceLastReduce < 3) {
                    return false;
                }
            }
            
            return true;
            
        } catch (Exception e) {
            logger.error("检查减仓条件异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 🔥 简化：只输出支撑阻力位信息，去除维加斯通道分析
     */
    private void logSimplifiedMarketAnalysis(String symbol, BigDecimal currentPrice) {
        try {
            logger.info("[{}] MARKET_ANALYSIS_START - 开始市场分析（简化版）", symbol);
            
            // 只检测并输出支撑阻力位信息
            checkAndLogSupportResistanceLevels(symbol, currentPrice);
            
            logger.info("[{}] MARKET_ANALYSIS_END - 市场分析完成", symbol);
            
        } catch (Exception e) {
            logger.error("[{}] MARKET_ANALYSIS_ERROR - 市场分析异常: {}", symbol, e.getMessage());
        }
    }

    /**
     * 检测并输出支撑阻力位信息（优化版本：显示最近5个）
     */
    private void checkAndLogSupportResistanceLevels(String symbol, BigDecimal currentPrice) {
        try {
            logger.debug("[{}] SUPPORT_RESISTANCE_ANALYSIS_START - 开始支撑阻力位分析", symbol);
            
            // 获取4小时支撑阻力位检测结果
            SupportResistanceDetector.SupportResistanceResult result = 
                supportResistanceDetector.detectLevels(symbol, "4h");
            
            if (result != null) {
                List<BigDecimal> supportLevels = result.getSupportLevels();
                List<BigDecimal> resistanceLevels = result.getResistanceLevels();
                
                // 🔥 优化：显示最近的5个支撑位和5个阻力位
                StringBuilder levelInfo = new StringBuilder();
                
                // 输出最近5个支撑位信息
                if (!supportLevels.isEmpty()) {
                    // 获取当前价格下方最近的5个支撑位
                    List<BigDecimal> nearestSupports = supportLevels.stream()
                        .filter(support -> support.compareTo(currentPrice) < 0) // 只要低于当前价格的
                        .sorted((a, b) -> b.compareTo(a)) // 从高到低排序（离当前价格近的在前）
                        .limit(5) // 取前5个
                        .collect(java.util.stream.Collectors.toList());
                    
                    if (!nearestSupports.isEmpty()) {
                        levelInfo.append("支撑: ");
                        for (int i = 0; i < nearestSupports.size(); i++) {
                            BigDecimal support = nearestSupports.get(i);
                            BigDecimal distance = currentPrice.subtract(support)
                                .divide(currentPrice, 4, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal("100"));
                            levelInfo.append(String.format("%s(-%.1f%%)", support.toPlainString(), distance.doubleValue()));
                            if (i < nearestSupports.size() - 1) {
                                levelInfo.append(", ");
                            }
                        }
                        levelInfo.append(" | ");
                    } else {
                        levelInfo.append("支撑: 无 | ");
                    }
                } else {
                    levelInfo.append("支撑: 无 | ");
                }
                
                // 输出最近5个阻力位信息
                if (!resistanceLevels.isEmpty()) {
                    // 获取当前价格上方最近的5个阻力位
                    List<BigDecimal> nearestResistances = resistanceLevels.stream()
                        .filter(resistance -> resistance.compareTo(currentPrice) > 0) // 只要高于当前价格的
                        .sorted(BigDecimal::compareTo) // 从低到高排序（离当前价格近的在前）
                        .limit(5) // 取前5个
                        .collect(java.util.stream.Collectors.toList());
                    
                    if (!nearestResistances.isEmpty()) {
                        levelInfo.append("阻力: ");
                        for (int i = 0; i < nearestResistances.size(); i++) {
                            BigDecimal resistance = nearestResistances.get(i);
                            BigDecimal distance = resistance.subtract(currentPrice)
                                .divide(currentPrice, 4, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal("100"));
                            levelInfo.append(String.format("%s(+%.1f%%)", resistance.toPlainString(), distance.doubleValue()));
                            if (i < nearestResistances.size() - 1) {
                                levelInfo.append(", ");
                            }
                        }
                    } else {
                        levelInfo.append("阻力: 无");
                    }
                } else {
                    levelInfo.append("阻力: 无");
                }
                
                // 检查是否接近关键位置
                boolean nearSupport = result.isNearSupportLevel(currentPrice, 0.03); // 3%范围内
                boolean nearResistance = result.isNearResistanceLevel(currentPrice, 0.03); // 3%范围内
                
                String zoneInfo = "";
                if (nearSupport && nearResistance) {
                    zoneInfo = "【关键区域】";
                } else if (nearSupport) {
                    zoneInfo = "【接近支撑】";
                } else if (nearResistance) {
                    zoneInfo = "【接近阻力】";
                } else {
                    zoneInfo = "【中性区域】";
                }
                
                // 🔥 优化：合并为一行简洁的日志，显示最近5个位置
                logger.info("[{}] 📊 LEVELS(TOP5) - {} {}", symbol, levelInfo.toString().trim(), zoneInfo);
                
            } else {
                logger.debug("[{}] SUPPORT_RESISTANCE_FAILED - 支撑阻力位检测失败", symbol);
            }
            
        } catch (Exception e) {
            logger.error("[{}] SUPPORT_RESISTANCE_ERROR - 支撑阻力位分析异常: {}", symbol, e.getMessage());
        }
    }

    private void checkAndExecuteAddPosition(String symbol) {
        // 检查多单加仓
        Optional<Position> longPosition = tradingService.getActivePosition(symbol, "LONG");
        if (longPosition.isPresent()) {
                    Position position = longPosition.get();
            
            // 🔥 唯一加仓策略：反转站稳市价加仓
            boolean shouldMarketAddOnReversal = addPositionStrategy.shouldMarketAddOnReversal(symbol, position);
            if (shouldMarketAddOnReversal) {
                // 🔥 强化：价格距离保护，避免同位置反复加仓
                    BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
                BigDecimal minDistanceFromHistory = position.getMinDistanceFromHistory(currentPrice);
                
                // 🔥 保持原有的2%距离要求不变
                BigDecimal requiredDistance = new BigDecimal("2.0");
                
                if (minDistanceFromHistory.compareTo(requiredDistance) >= 0) {
                    // 执行市价加仓
                    BigDecimal currentPositionValue = position.getQuantity().multiply(currentPrice);
                    
                    // 🔥 翻倍加仓策略：加仓金额 = 当前仓位价值
                    BigDecimal plannedAddValue = currentPositionValue;
                    
                    // 🔥 风险控制：多空合计总仓位不超过1000U
                    BigDecimal finalAddValue = checkAndAdjustPositionSize(symbol, currentPrice, plannedAddValue, "多单反转市价加仓");
                    
                    if (finalAddValue.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal addQuantity = finalAddValue.divide(currentPrice, tradingService.getQuantityPrecision(symbol), RoundingMode.HALF_UP);
                    
                        BigDecimal currentPnl = position.calculatePnl(currentPrice);
                        BigDecimal lossPercentage = currentPnl.abs().divide(position.getTotalAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                        
                        logger.info("[{}] 🚀 多单反转站稳市价加仓：亏损{}% ≥ {}% + MA25反转站稳 + 价格距离{}%≥2% | 当前仓位价值{}U，加仓{}U，数量{} | 策略：抓住反转时机", 
                                   symbol, lossPercentage.toPlainString(), tradingConfig.getAddPosition().getReversalLossThreshold().toPlainString(),
                                   minDistanceFromHistory.toPlainString(), currentPositionValue.toPlainString(), finalAddValue.toPlainString(), addQuantity.toPlainString());
                        
                        boolean success = tradingService.addPosition(symbol, "BUY", addQuantity, "LONG", "REVERSAL_MARKET_ADD_LONG");
                        if (success) {
                            logger.info("[{}] 🎉 多单反转站稳市价加仓成功", symbol);
                    } else {
                            logger.warn("[{}] ❌ 多单反转站稳市价加仓失败", symbol);
                }
                    } else {
                        logger.warn("[{}] 多单反转站稳市价加仓被风控拒绝：总仓位已达1000U限制", symbol);
                    }
                } else {
                    logger.info("[{}] 多单反转站稳加仓被拒绝：价格距离{}%<2%，避免同位置反复加仓", 
                               symbol, minDistanceFromHistory.toPlainString());
                }
            }
        }

        // 检查空单加仓（相同逻辑）
        Optional<Position> shortPosition = tradingService.getActivePosition(symbol, "SHORT");
        if (shortPosition.isPresent()) {
                    Position position = shortPosition.get();
            
            // 🔥 唯一加仓策略：反转站稳市价加仓
            boolean shouldMarketAddOnReversal = addPositionStrategy.shouldMarketAddOnReversal(symbol, position);
            if (shouldMarketAddOnReversal) {
                // 🔥 强化：价格距离保护，避免同位置反复加仓
                    BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
                BigDecimal minDistanceFromHistory = position.getMinDistanceFromHistory(currentPrice);
                
                // 🔥 保持原有的2%距离要求不变
                BigDecimal requiredDistance = new BigDecimal("2.0");
                
                if (minDistanceFromHistory.compareTo(requiredDistance) >= 0) {
                    // 执行市价加仓
                    BigDecimal currentPositionValue = position.getQuantity().multiply(currentPrice);
                    
                    // 🔥 翻倍加仓策略：加仓金额 = 当前仓位价值
                    BigDecimal plannedAddValue = currentPositionValue;
                    
                    // 🔥 风险控制：多空合计总仓位不超过1000U
                    BigDecimal finalAddValue = checkAndAdjustPositionSize(symbol, currentPrice, plannedAddValue, "空单反转市价加仓");
                    
                    if (finalAddValue.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal addQuantity = finalAddValue.divide(currentPrice, tradingService.getQuantityPrecision(symbol), RoundingMode.HALF_UP);
                    
                        BigDecimal currentPnl = position.calculatePnl(currentPrice);
                        BigDecimal lossPercentage = currentPnl.abs().divide(position.getTotalAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                        
                        logger.info("[{}] 🚀 空单反转站稳市价加仓：亏损{}% ≥ {}% + MA25反转站稳 + 价格距离{}%≥2% | 当前仓位价值{}U，加仓{}U，数量{} | 策略：抓住反转时机", 
                                   symbol, lossPercentage.toPlainString(), tradingConfig.getAddPosition().getReversalLossThreshold().toPlainString(),
                                   minDistanceFromHistory.toPlainString(), currentPositionValue.toPlainString(), finalAddValue.toPlainString(), addQuantity.toPlainString());
                        
                        boolean success = tradingService.addPosition(symbol, "SELL", addQuantity, "SHORT", "REVERSAL_MARKET_ADD_SHORT");
                        if (success) {
                            logger.info("[{}] 🎉 空单反转站稳市价加仓成功", symbol);
                    } else {
                            logger.warn("[{}] ❌ 空单反转站稳市价加仓失败", symbol);
                }
                    } else {
                        logger.warn("[{}] 空单反转站稳市价加仓被风控拒绝：总仓位已达1000U限制", symbol);
                    }
                } else {
                    logger.info("[{}] 空单反转站稳加仓被拒绝：价格距离{}%<2%，避免同位置反复加仓", 
                               symbol, minDistanceFromHistory.toPlainString());
                }
            }
        }
    }

    /**
     * 处理未成交的限价单
     * @param symbol 交易对
     * @param positionSide 持仓方向
     * @param pendingOrders 未成交的订单列表
     * @param currentAddPrice 当前合理的加仓价格
     * @return 是否应该下新的限价单
     */
    private boolean handlePendingOrders(String symbol, String positionSide, List<TradeRecord> pendingOrders, BigDecimal currentAddPrice) {
        if (pendingOrders.isEmpty()) {
            return true; // 没有未成交订单，可以下新单
        }

        for (TradeRecord pendingOrder : pendingOrders) {
            BigDecimal orderPrice = pendingOrder.getPrice();
            
            // 检查旧订单价格是否仍然合理（容忍1%偏差）
            boolean priceReasonable = tradingService.isOrderPriceReasonable(orderPrice, currentAddPrice, 1.0);
            
            if (priceReasonable) {
                logger.info("[{}] {}限价单{}价格{}仍然合理，等待成交", symbol, positionSide, pendingOrder.getOrderId(), orderPrice);
                return false; // 等待现有订单成交，不下新单
            } else {
                logger.info("[{}] {}限价单{}价格{}已不合理，当前合理价格{}，取消并重新挂单", 
                           symbol, positionSide, pendingOrder.getOrderId(), orderPrice, currentAddPrice);
                // 🔥 改进：使用安全的取消方法，忽略订单不存在错误
                safeCancelLimitOrder(symbol, pendingOrder.getOrderId());
            }
        }
        
        return true; // 所有旧订单都已取消，可以下新单
    }
    
    /**
     * 🔥 生生不息核心：安排快速重新开仓检查
     * @param symbol 交易对
     * @param justClosedSide 刚刚平仓的方向
     * @param currentPrice 当前价格
     */
    private void scheduleQuickReopenCheck(String symbol, String justClosedSide, BigDecimal currentPrice) {
        logger.info("[{}] 🔄 生生不息：平仓完成，开始快速重新开仓检查 | 刚平仓方向: {}", symbol, justClosedSide);
        
        // 异步检查快速重新开仓机会（避免阻塞主流程）
        new Thread(() -> {
            try {
                // 🔥 根据平仓类型设置不同的冷静期
                long cooldownMillis;
                if ("HEDGE".equals(justClosedSide)) {
                    cooldownMillis = 600000; // 对冲平仓：冷静10分钟
                    logger.info("[{}] 🔄 对冲平仓冷静期：等待10分钟让市场充分消化", symbol);
                } else {
                    cooldownMillis = 120000; // 单向平仓：冷静2分钟
                    logger.info("[{}] 🔄 单向平仓冷静期：等待2分钟让市场消化", symbol);
                }
                
                Thread.sleep(cooldownMillis);
                
                logger.info("[{}] 🔄 生生不息：开始检查快速重新开仓机会", symbol);
                
                // 1. 检查是否应该快速重新开仓
                boolean shouldQuickReopen = positionSizingService.shouldQuickReopen(symbol, justClosedSide, currentPrice);
                
                if (shouldQuickReopen) {
                    // 2. 获取新的价格
                    BigDecimal newPrice = tradingService.getCurrentPrice(symbol);
                    
                    // 3. 检查反向信号
                    String oppositeDirection = "LONG".equals(justClosedSide) ? "SHORT" : "LONG";
                    boolean oppositeSignal = checkOppositeSignal(symbol, oppositeDirection);
                    
                    if (oppositeSignal) {
                        logger.info("[{}] 🚀 生生不息：检测到反向信号，快速开{}仓", symbol, oppositeDirection);
                        
                        // 4. 🔥 智能对冲机制：检查是否需要对冲开仓
                        BigDecimal reopenPositionSize;
                        String reopenReason;
                        
                        // 检查是否有反向持仓需要对冲
                        Optional<Position> existingOppositePosition = null;
                        if ("LONG".equals(oppositeDirection)) {
                            existingOppositePosition = tradingService.getActivePosition(symbol, "SHORT");
                        } else {
                            existingOppositePosition = tradingService.getActivePosition(symbol, "LONG");
                        }
                        
                        if (existingOppositePosition.isPresent()) {
                            // 有反向持仓：固定30U对冲重开，避免锁死
                            Position oppositePos = existingOppositePosition.get();
                            BigDecimal oppositePositionValue = oppositePos.getQuantity().multiply(newPrice);
                            
                            // 🔥 修复：固定30U对冲重开，避免等额锁死
                            reopenPositionSize = tradingConfig.getDynamicPosition().getBasePositionUsdt(); // 固定30U
                            reopenReason = "QUICK_REOPEN_HEDGE_FIXED_" + oppositeDirection;
                            
                            // 🔥 重要：保留总仓位不超过1000U的风险控制
                            BigDecimal finalReopenSize = checkAndAdjustPositionSize(symbol, newPrice, reopenPositionSize, "快速固定对冲重开" + oppositeDirection);
                            
                            if (finalReopenSize.compareTo(BigDecimal.ZERO) <= 0) {
                                logger.warn("[{}] 🔄 快速固定对冲重开{}被风控拒绝：总仓位已达1000U限制", symbol, oppositeDirection);
                                return;
                            }
                            
                            reopenPositionSize = finalReopenSize;
                            
                            logger.info("[{}] 🔄 快速固定对冲重开：{}持仓价值{}U，计划固定重开30U，实际重开{}U（1000U限制保护）", 
                                       symbol, 
                                       "LONG".equals(oppositeDirection) ? "空单" : "多单",
                                       oppositePositionValue.toPlainString(), 
                                       reopenPositionSize.toPlainString());
                        } else {
                            // 无反向持仓：使用固定仓位大小（风险控制）
                            BigDecimal plannedReopenSize = tradingConfig.getDynamicPosition().getBasePositionUsdt(); // 固定30U
                            reopenReason = "QUICK_REOPEN_" + oppositeDirection;
                            
                            // 🔥 新的风险控制：确保总仓位不超过1000U
                            BigDecimal finalReopenSize = checkAndAdjustPositionSize(symbol, newPrice, plannedReopenSize, "快速常规重开" + oppositeDirection);
                            
                            if (finalReopenSize.compareTo(BigDecimal.ZERO) <= 0) {
                                logger.warn("[{}] 🔄 快速常规重开{}被风控拒绝：总仓位已达1000U限制", symbol, oppositeDirection);
                                return;
                            }
                            
                            reopenPositionSize = finalReopenSize;
                            
                            logger.info("[{}] 💰 快速常规重开：无反向持仓，计划重开{}U，实际重开{}U（总限制1000U）", 
                                       symbol, plannedReopenSize.toPlainString(), reopenPositionSize.toPlainString());
                        }
                        
                        BigDecimal quantity = reopenPositionSize.divide(newPrice, tradingService.getQuantityPrecision(symbol), RoundingMode.HALF_UP);
                        
                        // 5. 执行快速重新开仓
                        if ("LONG".equals(oppositeDirection)) {
                            boolean success = tradingService.openPosition(symbol, "BUY", quantity, "LONG", reopenReason);
                            if (success) {
                                if (existingOppositePosition.isPresent()) {
                                    logger.info("[{}] 🎉 生生不息：快速固定对冲重开多成功！仓位{}U（避免锁死）", symbol, reopenPositionSize.toPlainString());
                                } else {
                                    logger.info("[{}] 🎉 生生不息：快速重新开多成功！仓位{}U", symbol, reopenPositionSize.toPlainString());
                                }
                            }
                        } else {
                            boolean success = tradingService.openPosition(symbol, "SELL", quantity, "SHORT", reopenReason);
                            if (success) {
                                if (existingOppositePosition.isPresent()) {
                                    logger.info("[{}] 🎉 生生不息：快速对冲重开空成功！仓位{}U（总限制1000U）", symbol, reopenPositionSize.toPlainString());
                                } else {
                                    logger.info("[{}] 🎉 生生不息：快速重新开空成功！仓位{}U（总限制1000U）", symbol, reopenPositionSize.toPlainString());
                                }
                            }
                        }
                    } else {
                        logger.info("[{}] 🔄 生生不息：暂无反向信号，继续等待机会", symbol);
                    }
                } else {
                    logger.info("[{}] 🔄 生生不息：不满足快速重新开仓条件", symbol);
                }
                
            } catch (Exception e) {
                logger.error("[{}] 生生不息快速重新开仓异常: {}", symbol, e.getMessage());
            }
        }).start();
    }
    
    /**
     * 检查反向信号
     * @param symbol 交易对
     * @param direction 方向
     * @return 是否有反向信号
     */
    private boolean checkOppositeSignal(String symbol, String direction) {
        try {
            if ("LONG".equals(direction)) {
                return ma25TradingStrategy.checkLongOpenSignal(symbol);
            } else {
                return ma25TradingStrategy.checkShortOpenSignal(symbol);
            }
        } catch (Exception e) {
            logger.error("[{}] 检查反向信号异常: {}", symbol, e.getMessage());
            return false;
        }
    }

    /**
     * 检查指定交易对的限价单状态
     */
    private void checkSymbolLimitOrders(String symbol) {
        try {
            // 🔥 修复：改进查询条件，只查询真正待处理的订单
            List<TradeRecord> pendingOrders = tradeRecordRepository.findAll().stream()
                .filter(record -> symbol.equals(record.getSymbol()))
                .filter(record -> "LIMIT".equals(record.getType()))
                .filter(record -> Arrays.asList("NEW", "PARTIALLY_FILLED").contains(record.getStatus()))
                .filter(record -> record.getOrderId() != null)
                .collect(Collectors.toList());
            
            if (pendingOrders.isEmpty()) {
                logger.debug("[{}] 无待处理的限价单", symbol);
                return;
            }
            
            logger.debug("[{}] 检查{}个待处理限价单状态", symbol, pendingOrders.size());
            
            for (TradeRecord order : pendingOrders) {
                try {
                    // 🔥 新增：检查订单记录的有效性
                    if (order.getCreatedAt() != null) {
                        long hoursAge = java.time.Duration.between(order.getCreatedAt(), LocalDateTime.now()).toHours();
                        if (hoursAge > 24) {
                            // 超过24小时的订单直接标记为过期，避免无效API调用
                            logger.info("[{}] 限价单{}已超过24小时，直接标记为过期", symbol, order.getOrderId());
                            order.setStatus("EXPIRED");
                            order.setUpdatedAt(LocalDateTime.now());
                            tradeRecordRepository.save(order);
                            continue;
                        }
                    }

                    // 🔥 新增：检查价格偏离是否过大（5%容忍度）
                    BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
                    if (currentPrice != null && currentPrice.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal orderPrice = order.getPrice();
                        if (orderPrice != null) {
                            // 计算价格偏离百分比
                            BigDecimal priceDifference = orderPrice.subtract(currentPrice).abs();
                            BigDecimal deviationPercentage = priceDifference.divide(currentPrice, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));

                            // 如果偏离超过5%，取消订单
                            if (deviationPercentage.compareTo(new BigDecimal("5.0")) > 0) {
                                logger.info("[{}] 限价单{}价格偏离过大：订单价格{} vs 当前价格{}，偏离{}%>5%，取消订单",
                                           symbol, order.getOrderId(), orderPrice.toPlainString(),
                                           currentPrice.toPlainString(), deviationPercentage.toPlainString());

                                // 安全取消限价单
                                safeCancelLimitOrder(symbol, order.getOrderId());
                                continue;
                            }
                        }
                    }

                    // 检查订单状态并更新持仓
                    boolean isCompleted = tradingService.updateLimitOrderAndPosition(symbol, order.getOrderId());
                    if (isCompleted) {
                        logger.info("[{}] 限价单{}状态更新完成", symbol, order.getOrderId());
                    } else {
                        logger.debug("[{}] 限价单{}仍在处理中", symbol, order.getOrderId());
                    }
                    
                    // 🔥 新增：添加短暂延迟，避免频繁API调用
                    Thread.sleep(100);
                    
                } catch (Exception e) {
                    String errorMsg = e.getMessage();
                    if (errorMsg != null && (errorMsg.contains("Unknown order") || errorMsg.contains("ORDER_NOT_EXIST"))) {
                        // 订单不存在，直接标记为取消状态
                        logger.info("[{}] 限价单{}不存在于币安，标记为已取消", symbol, order.getOrderId());
                        order.setStatus("CANCELED");
                        order.setUpdatedAt(LocalDateTime.now());
                        tradeRecordRepository.save(order);
                    } else {
                        logger.warn("[{}] 检查限价单{}状态异常: {}", symbol, order.getOrderId(), errorMsg);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("[{}] 检查限价单状态整体异常: {}", symbol, e.getMessage());
        }
    }

    /**
     * 🔥 新增：计算指定交易对的多空合计仓位价值
     * @param symbol 交易对
     * @param currentPrice 当前价格
     * @return 多空合计仓位价值（USDT）
     */
    private BigDecimal calculateTotalPositionValue(String symbol, BigDecimal currentPrice) {
        try {
            BigDecimal totalValue = BigDecimal.ZERO;
            
            // 计算多单价值
            Optional<Position> longPosition = tradingService.getActivePosition(symbol, "LONG");
            if (longPosition.isPresent()) {
                BigDecimal longValue = longPosition.get().getQuantity().multiply(currentPrice);
                totalValue = totalValue.add(longValue);
            }
            
            // 计算空单价值
            Optional<Position> shortPosition = tradingService.getActivePosition(symbol, "SHORT");
            if (shortPosition.isPresent()) {
                BigDecimal shortValue = shortPosition.get().getQuantity().multiply(currentPrice);
                totalValue = totalValue.add(shortValue);
            }
            
            return totalValue;
            
        } catch (Exception e) {
            logger.error("[{}] 计算总仓位价值异常: {}", symbol, e.getMessage());
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 🔥 新增：检查仓位风险控制 - 多空合计不超过1000U
     * @param symbol 交易对
     * @param currentPrice 当前价格
     * @param plannedOpenSize 计划开仓金额
     * @param actionType 操作类型（用于日志）
     * @return 调整后的开仓金额
     */
    private BigDecimal checkAndAdjustPositionSize(String symbol, BigDecimal currentPrice, BigDecimal plannedOpenSize, String actionType) {
        try {
            // 计算当前总仓位价值
            BigDecimal currentTotalValue = calculateTotalPositionValue(symbol, currentPrice);
            
            // 设置总仓位限制为1000U
            BigDecimal maxTotalPosition = new BigDecimal("1000");
            
            // 计算可用空间
            BigDecimal availableSpace = maxTotalPosition.subtract(currentTotalValue);
            
            if (availableSpace.compareTo(BigDecimal.ZERO) <= 0) {
                logger.warn("[{}] 🔥 {}被拒绝：当前总仓位{}U已达到限制1000U，无可用空间", 
                           symbol, actionType, currentTotalValue.toPlainString());
                return BigDecimal.ZERO;
            }
            
            if (plannedOpenSize.compareTo(availableSpace) <= 0) {
                // 计划开仓金额在限制范围内
                logger.info("[{}] 🔥 {}风控检查通过：计划{}U，当前总仓位{}U，剩余空间{}U", 
                           symbol, actionType, plannedOpenSize.toPlainString(), 
                           currentTotalValue.toPlainString(), availableSpace.toPlainString());
                return plannedOpenSize;
            } else {
                // 需要调整开仓金额
                BigDecimal adjustedSize = availableSpace;
                logger.warn("[{}] 🔥 {}金额调整：原计划{}U > 可用空间{}U，调整为{}U | 当前总仓位{}U/1000U", 
                           symbol, actionType, plannedOpenSize.toPlainString(), 
                           availableSpace.toPlainString(), adjustedSize.toPlainString(), 
                           currentTotalValue.toPlainString());
                return adjustedSize;
            }
            
        } catch (Exception e) {
            logger.error("[{}] 检查仓位风险控制异常: {}", symbol, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 🔥 检查是否有对冲持仓（多空双持）
     */
    private boolean hasHedgePositions(String symbol) {
        Optional<Position> longPosition = tradingService.getActivePosition(symbol, "LONG");
        Optional<Position> shortPosition = tradingService.getActivePosition(symbol, "SHORT");
        return longPosition.isPresent() && shortPosition.isPresent();
    }
    
    /**
     * 🔥 检查对冲操作冷却期
     * @param symbol 交易对
     * @param operationType 操作类型
     * @param cooldownMinutes 冷却分钟数
     * @return 是否可以执行操作
     */
    private boolean canExecuteHedgeOperation(String symbol, String operationType, int cooldownMinutes) {
        String key = symbol + "_" + operationType;
        LocalDateTime lastOperation = hedgeOperationCooldown.get(key);
        
        if (lastOperation == null) {
            return true; // 没有历史记录，可以执行
        }
        
        long minutesSinceLastOperation = java.time.Duration.between(lastOperation, LocalDateTime.now()).toMinutes();
        if (minutesSinceLastOperation >= cooldownMinutes) {
            return true; // 冷却期已过，可以执行
        }
        
        logger.debug("[{}] 对冲操作冷却中：{}，距离上次操作{}分钟，需要冷却{}分钟", 
                    symbol, operationType, minutesSinceLastOperation, cooldownMinutes);
        return false;
    }
    
    /**
     * 🔥 记录对冲操作时间
     * @param symbol 交易对
     * @param operationType 操作类型
     */
    private void recordHedgeOperation(String symbol, String operationType) {
        String key = symbol + "_" + operationType;
        hedgeOperationCooldown.put(key, LocalDateTime.now());
        logger.debug("[{}] 记录对冲操作：{}", symbol, operationType);
    }
    
    /**
     * 🔥 新设计：灵活的对冲止盈策略
     * 核心思想：盈利的一方在阻力位/支撑位灵活减仓，而不是等MA25反转
     */
    private boolean handleFlexibleHedgeStrategy(String symbol) {
        try {
            Optional<Position> longPosition = tradingService.getActivePosition(symbol, "LONG");
            Optional<Position> shortPosition = tradingService.getActivePosition(symbol, "SHORT");
            
            if (!longPosition.isPresent() || !shortPosition.isPresent()) {
                return false; // 不是对冲持仓
            }
            
            Position longPos = longPosition.get();
            Position shortPos = shortPosition.get();
            
            // 🔥 新增：空值安全检查
            if (longPos == null || shortPos == null) {
                logger.warn("[{}] 对冲持仓对象为null，跳过处理", symbol);
                return false;
            }
            
            // 🔥 新增：检查关键字段的完整性
            if (longPos.getTotalAmount() == null || longPos.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0 ||
                shortPos.getTotalAmount() == null || shortPos.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
                logger.warn("[{}] 对冲持仓金额数据不完整：多单={}U, 空单={}U，跳过处理", 
                           symbol, 
                           longPos.getTotalAmount() != null ? longPos.getTotalAmount().toPlainString() : "null",
                           shortPos.getTotalAmount() != null ? shortPos.getTotalAmount().toPlainString() : "null");
                return false;
            }
            
            BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
            if (currentPrice == null || currentPrice.compareTo(BigDecimal.ZERO) <= 0) {
                logger.warn("[{}] 无法获取有效当前价格，跳过对冲策略", symbol);
                return false;
            }
            
            // 🔥 安全计算多空双边盈亏
            BigDecimal longPnl = BigDecimal.ZERO;
            BigDecimal shortPnl = BigDecimal.ZERO;
            try {
                longPnl = longPos.calculatePnl(currentPrice);
                shortPnl = shortPos.calculatePnl(currentPrice);
                
                if (longPnl == null) longPnl = BigDecimal.ZERO;
                if (shortPnl == null) shortPnl = BigDecimal.ZERO;
            } catch (Exception e) {
                logger.warn("[{}] 计算对冲盈亏异常: {}", symbol, e.getMessage());
                return false;
            }
            
            BigDecimal totalPnl = longPnl.add(shortPnl);
            
            // 🔥 安全计算盈亏百分比
            BigDecimal longPnlPercentage = BigDecimal.ZERO;
            BigDecimal shortPnlPercentage = BigDecimal.ZERO;
            BigDecimal totalPnlPercentage = BigDecimal.ZERO;
            
            try {
                if (longPos.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                    longPnlPercentage = longPnl.divide(longPos.getTotalAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                }
                if (shortPos.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                    shortPnlPercentage = shortPnl.divide(shortPos.getTotalAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                }
                
                BigDecimal totalInvestment = longPos.getTotalAmount().add(shortPos.getTotalAmount());
                if (totalInvestment.compareTo(BigDecimal.ZERO) > 0) {
                    totalPnlPercentage = totalPnl.divide(totalInvestment, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                }
            } catch (Exception e) {
                logger.warn("[{}] 计算对冲盈亏百分比异常: {}", symbol, e.getMessage());
                return false;
            }
            
            String longPnlSign = longPnl.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "";
            String shortPnlSign = shortPnl.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "";
            String totalPnlSign = totalPnl.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "";
            
            logger.info("[{}] 🔄 HEDGE_ANALYSIS - 对冲持仓分析:", symbol);
            logger.info("[{}]    ├─ 多单: {}U 盈亏{}{}U({}%)", symbol, longPos.getTotalAmount(), 
                       longPnlSign, longPnl.toPlainString(), longPnlPercentage.toPlainString());
            logger.info("[{}]    ├─ 空单: {}U 盈亏{}{}U({}%)", symbol, shortPos.getTotalAmount(), 
                       shortPnlSign, shortPnl.toPlainString(), shortPnlPercentage.toPlainString());
            logger.info("[{}]    └─ 对冲总计: 投入{}U 净盈亏{}{}U({}%)", symbol, longPos.getTotalAmount().add(shortPos.getTotalAmount()), 
                       totalPnlSign, totalPnl.toPlainString(), totalPnlPercentage.toPlainString());
            
            // 🔥 策略1：双边高盈利整体止盈
            if (totalPnlPercentage.compareTo(new BigDecimal("5.0")) > 0) {
                if (!canExecuteHedgeOperation(symbol, "TOTAL_HIGH_PROFIT", 5)) {
                    return false; // 冷却期内，跳过操作
                }
                
                logger.info("[{}] 🎯 HEDGE_HIGH_TOTAL_PROFIT - 对冲总盈利{}%>5%，整体止盈", symbol, totalPnlPercentage.toPlainString());
                executeFullHedgeClose(symbol, "HIGH_TOTAL_PROFIT", totalPnl);
                recordHedgeOperation(symbol, "TOTAL_HIGH_PROFIT");
                return true;
            }
            
            // 🔥 策略2：盈利一方接近阻力位/支撑位时灵活减仓（新增亏损检查 + 价格距离保护 + 小额持仓检查）
            boolean longProfitable = longPnlPercentage.compareTo(new BigDecimal("5.0")) > 0; // 🔥 提高门槛：多单盈利>5%
            boolean shortProfitable = shortPnlPercentage.compareTo(new BigDecimal("5.0")) > 0; // 🔥 提高门槛：空单盈利>5%
            
            if (longProfitable && longPnl.compareTo(BigDecimal.ZERO) > 0) { // 🔥 新增：必须盈利才能减仓
                // 🔥 修复：优先检查是否已经减仓过，确保"只减仓一次"逻辑
                if (longPos.isHasProfitReduced()) {
                    logger.debug("[{}] 多单已进行过盈利减仓，跳过阻力位减仓，等待MA25反转信号", symbol);
                } else {
                    // 🔥 新增：检查多单减仓冷却期
                    if (!canExecuteHedgeOperation(symbol, "LONG_RESISTANCE", 3)) {
                        logger.debug("[{}] 多单阻力位减仓冷却中，跳过", symbol);
                    } else {
                    // 🔥 新增：检查价格距离保护（≥2%）
                    boolean priceDistanceOk = checkPriceDistanceForReduce(longPos, currentPrice);
                    if (!priceDistanceOk) {
                        logger.debug("[{}] 多单阻力位减仓价格距离不足2%，跳过", symbol);
                    } else {
                        // 检查多单是否接近阻力位
                        try {
                            BigDecimal resistancePrice = supportResistanceDetector.getLatestResistance(symbol, "4h", currentPrice);
                            if (resistancePrice != null && resistancePrice.compareTo(BigDecimal.valueOf(Double.MAX_VALUE)) < 0) {
                                BigDecimal resistanceRange = resistancePrice.multiply(new BigDecimal("0.03"));
                                boolean nearResistance = currentPrice.subtract(resistancePrice).abs().compareTo(resistanceRange) <= 0;
                                
                                if (nearResistance) {
                                    BigDecimal resistanceDistance = resistancePrice.subtract(currentPrice).divide(currentPrice, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).abs();
                                    
                                    // 🔥 新增：检查是否小额持仓（<5U），如果是则完全平仓
                                    if (longPos.getTotalAmount().compareTo(new BigDecimal("5.0")) < 0) {
                                        logger.info("[{}] 🎯 HEDGE_LONG_RESISTANCE_SMALL - 多单小额持仓{}U，盈利{}%，接近阻力位，完全平仓", 
                                                   symbol, longPos.getTotalAmount().toPlainString(), longPnlPercentage.toPlainString());
                                        
                                        boolean closed = tradingService.closePosition(symbol, "SELL", BigDecimal.ONE, "HEDGE_LONG_RESISTANCE_SMALL_CLOSE");
                                        if (closed) {
                                            positionSizingService.recordTradeProfit(symbol, longPnl, "HEDGE_LONG_RESISTANCE_SMALL");
                                            recordHedgeOperation(symbol, "LONG_RESISTANCE_SMALL");
                                            return true;
                                        }
                                    } else {
                                        logger.info("[{}] 🎯 HEDGE_LONG_RESISTANCE - 多单盈利{}%≥4%+接近阻力位{}，距离{}%，减仓多单", 
                                                   symbol, longPnlPercentage.toPlainString(), resistancePrice.toPlainString(), resistanceDistance.toPlainString());
                                        
                                        // 🔥 修复：先减仓盈利的多单，再反向加仓到亏损的空单
                                        BigDecimal reducedAmount = BigDecimal.ZERO;
                                        
                                        if (longPos.hasAddedPosition()) {
                                            reducedAmount = longPos.getAddedAmount();
                                            tradingService.closeAddedPositionOnly(symbol, "LONG", "HEDGE_LONG_RESISTANCE_CLOSE_ADDED");
                                            // 🔥 修复：平掉加仓部分不设置hasProfitReduced标记，允许后续加仓减仓循环
                                        } else {
                                            reducedAmount = longPos.getTotalAmount().multiply(new BigDecimal("0.5"));
                                            tradingService.closePosition(symbol, "SELL", new BigDecimal("0.5"), "HEDGE_LONG_RESISTANCE_REDUCE");
                                        }
                                        
                                        // 🔥 新增：反向加仓到亏损的空单，保持对冲平衡
                                        if (reducedAmount.compareTo(BigDecimal.ZERO) > 0) {
                                            // 🔥 修复：同步处理对冲平衡加仓，避免数据不一致
                                            // 🔥 关键修复：只加仓减仓金额的一半（25%），避免对冲失衡放大
                                            BigDecimal hedgeAddValue = reducedAmount.multiply(new BigDecimal("0.5")); // 减仓的50%
                                            BigDecimal finalHedgeAddValue = checkAndAdjustPositionSize(symbol, currentPrice, hedgeAddValue, "阻力位对冲平衡加仓空单");
                                            
                                            // 🔥 新增：检查最小订单金额限制（5U）
                                            if (finalHedgeAddValue.compareTo(new BigDecimal("5.0")) >= 0) {
                                                BigDecimal addQuantity = finalHedgeAddValue.divide(currentPrice, tradingService.getQuantityPrecision(symbol), RoundingMode.HALF_UP);
                                                
                                                logger.info("[{}] 🔄 HEDGE_RESISTANCE_BALANCE - 阻力位减仓多单{}U后，反向加仓空单{}U(25%)", 
                                                           symbol, reducedAmount.toPlainString(), finalHedgeAddValue.toPlainString());
                                                
                                                boolean addSuccess = tradingService.addPosition(symbol, "SELL", addQuantity, "SHORT", "HEDGE_RESISTANCE_BALANCE_SHORT", true);
                                                if (addSuccess) {
                                                    logger.info("[{}] 🎉 HEDGE_RESISTANCE_BALANCE_SUCCESS - 阻力位对冲平衡成功", symbol);
                                                } else {
                                                    logger.warn("[{}] ⚠️ HEDGE_RESISTANCE_BALANCE_FAILED - 阻力位对冲平衡失败", symbol);
                                                }
                                            } else {
                                                logger.info("[{}] 🔄 HEDGE_RESISTANCE_BALANCE_SKIP - 阻力位对冲平衡加仓金额{}U < 5U，跳过加仓，等待趋势反转时再加仓", 
                                                           symbol, finalHedgeAddValue.toPlainString());
                                            }
                                        }
                                        
                                        // 🔥 记录减仓价格、时间和次数
                                        longPos.setLastReducePrice(currentPrice);
                                        longPos.setLastReduceTime(LocalDateTime.now());
                                        longPos.setReduceCount((longPos.getReduceCount() != null ? longPos.getReduceCount() : 0) + 1);
                                        positionRepository.save(longPos);
                                        
                                        positionSizingService.recordTradeProfit(symbol, longPnl, "HEDGE_LONG_RESISTANCE");
                                        recordHedgeOperation(symbol, "LONG_RESISTANCE");
                                        return true;
                                    }
                                }
                            }
                        } catch (Exception e) {
                            logger.warn("[{}] 多单阻力位检测异常，跳过: {}", symbol, e.getMessage());
                        }
                    }
                }
                }
            } else if (longProfitable && longPnl.compareTo(BigDecimal.ZERO) <= 0) {
                logger.debug("[{}] 多单虽然满足盈利百分比但实际亏损，跳过阻力位减仓", symbol);
            }
            
            if (shortProfitable && shortPnl.compareTo(BigDecimal.ZERO) > 0) { // 🔥 新增：必须盈利才能减仓
                // 🔥 修复：优先检查是否已经减仓过，确保"只减仓一次"逻辑
                if (shortPos.isHasProfitReduced()) {
                    logger.debug("[{}] 空单已进行过盈利减仓，跳过支撑位减仓，等待MA25反转信号", symbol);
                } else {
                    // 🔥 新增：检查空单减仓冷却期
                    if (!canExecuteHedgeOperation(symbol, "SHORT_SUPPORT", 3)) {
                        logger.debug("[{}] 空单支撑位减仓冷却中，跳过", symbol);
                    } else {
                    // 🔥 新增：检查价格距离保护（≥2%）
                    boolean priceDistanceOk = checkPriceDistanceForReduce(shortPos, currentPrice);
                    if (!priceDistanceOk) {
                        logger.debug("[{}] 空单支撑位减仓价格距离不足2%，跳过", symbol);
                    } else {
                        // 检查空单是否接近支撑位
                        try {
                            BigDecimal supportPrice = supportResistanceDetector.getLatestSupport(symbol, "4h", currentPrice);
                            if (supportPrice != null && supportPrice.compareTo(BigDecimal.ZERO) > 0) {
                                BigDecimal supportRange = supportPrice.multiply(new BigDecimal("0.03"));
                                boolean nearSupport = currentPrice.subtract(supportPrice).abs().compareTo(supportRange) <= 0;
                                
                                if (nearSupport) {
                                    BigDecimal supportDistance = currentPrice.subtract(supportPrice).divide(currentPrice, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).abs();
                                    
                                    // 🔥 新增：检查是否小额持仓（<5U），如果是则完全平仓
                                    if (shortPos.getTotalAmount().compareTo(new BigDecimal("5.0")) < 0) {
                                        logger.info("[{}] 🎯 HEDGE_SHORT_SUPPORT_SMALL - 空单小额持仓{}U，盈利{}%，接近支撑位，完全平仓", 
                                                   symbol, shortPos.getTotalAmount().toPlainString(), shortPnlPercentage.toPlainString());
                                        
                                        boolean closed = tradingService.closePosition(symbol, "BUY", BigDecimal.ONE, "HEDGE_SHORT_SUPPORT_SMALL_CLOSE");
                                        if (closed) {
                                            positionSizingService.recordTradeProfit(symbol, shortPnl, "HEDGE_SHORT_SUPPORT_SMALL");
                                            recordHedgeOperation(symbol, "SHORT_SUPPORT_SMALL");
                                            return true;
                                        }
                                    } else {
                                        logger.info("[{}] 🎯 HEDGE_SHORT_SUPPORT - 空单盈利{}%≥4%+接近支撑位{}，距离{}%，减仓空单", 
                                                   symbol, shortPnlPercentage.toPlainString(), supportPrice.toPlainString(), supportDistance.toPlainString());
                                        
                                        // 🔥 修复：先减仓盈利的空单，再反向加仓到亏损的多单
                                        BigDecimal reducedAmount = BigDecimal.ZERO;
                                        
                                        if (shortPos.hasAddedPosition()) {
                                            reducedAmount = shortPos.getAddedAmount();
                                            tradingService.closeAddedPositionOnly(symbol, "SHORT", "HEDGE_SHORT_SUPPORT_CLOSE_ADDED");
                                            // 🔥 修复：平掉加仓部分不设置hasProfitReduced标记，允许后续加仓减仓循环
                                        } else {
                                            reducedAmount = shortPos.getTotalAmount().multiply(new BigDecimal("0.5"));
                                            tradingService.closePosition(symbol, "BUY", new BigDecimal("0.5"), "HEDGE_SHORT_SUPPORT_REDUCE");
                                        }
                                        
                                        // 🔥 新增：反向加仓到亏损的多单，保持对冲平衡
                                        if (reducedAmount.compareTo(BigDecimal.ZERO) > 0) {
                                            // 🔥 修复：同步处理对冲平衡加仓，避免数据不一致
                                            // 🔥 关键修复：只加仓减仓金额的一半（25%），避免对冲失衡放大
                                            BigDecimal hedgeAddValue = reducedAmount.multiply(new BigDecimal("0.5")); // 减仓的50%
                                            BigDecimal finalHedgeAddValue = checkAndAdjustPositionSize(symbol, currentPrice, hedgeAddValue, "支撑位对冲平衡加仓多单");
                                            
                                            // 🔥 新增：检查最小订单金额限制（5U）
                                            if (finalHedgeAddValue.compareTo(new BigDecimal("5.0")) >= 0) {
                                                BigDecimal addQuantity = finalHedgeAddValue.divide(currentPrice, tradingService.getQuantityPrecision(symbol), RoundingMode.HALF_UP);
                                                
                                                logger.info("[{}] 🔄 HEDGE_SUPPORT_BALANCE - 支撑位减仓空单{}U后，反向加仓多单{}U(25%)", 
                                                           symbol, reducedAmount.toPlainString(), finalHedgeAddValue.toPlainString());
                                                
                                                boolean addSuccess = tradingService.addPosition(symbol, "BUY", addQuantity, "LONG", "HEDGE_SUPPORT_BALANCE_LONG", true);
                                                if (addSuccess) {
                                                    logger.info("[{}] 🎉 HEDGE_SUPPORT_BALANCE_SUCCESS - 支撑位对冲平衡成功", symbol);
                                                } else {
                                                    logger.warn("[{}] ⚠️ HEDGE_SUPPORT_BALANCE_FAILED - 支撑位对冲平衡失败", symbol);
                                                }
                                            } else {
                                                logger.info("[{}] 🔄 HEDGE_SUPPORT_BALANCE_SKIP - 支撑位对冲平衡加仓金额{}U < 5U，跳过加仓，等待趋势反转时再加仓", 
                                                           symbol, finalHedgeAddValue.toPlainString());
                                            }
                                        }
                                        
                                        // 🔥 记录减仓价格、时间和次数
                                        shortPos.setLastReducePrice(currentPrice);
                                        shortPos.setLastReduceTime(LocalDateTime.now());
                                        shortPos.setReduceCount((shortPos.getReduceCount() != null ? shortPos.getReduceCount() : 0) + 1);
                                        positionRepository.save(shortPos);
                                        
                                        positionSizingService.recordTradeProfit(symbol, shortPnl, "HEDGE_SHORT_SUPPORT");
                                        recordHedgeOperation(symbol, "SHORT_SUPPORT");
                                        return true;
                                    }
                                }
                            }
                        } catch (Exception e) {
                            logger.warn("[{}] 空单支撑位检测异常，跳过: {}", symbol, e.getMessage());
                        }
                    }
                }
                }
            } else if (shortProfitable && shortPnl.compareTo(BigDecimal.ZERO) <= 0) {
                logger.debug("[{}] 空单虽然满足盈利百分比但实际亏损，跳过支撑位减仓", symbol);
            }
            
            // 🔥 策略3：单边高盈利+阻力位减仓（集成阻力位检查 + 对冲单亏损检查）
            if (longPnlPercentage.compareTo(new BigDecimal("5.0")) > 0 && longPnl.compareTo(BigDecimal.ZERO) > 0) { // 🔥 新增：必须盈利才能减仓
                // 🔥 修复：优先检查是否已经减仓过，确保"只减仓一次"逻辑
                if (longPos.isHasProfitReduced()) {
                    logger.debug("[{}] 多单已进行过盈利减仓，跳过高盈利减仓，等待MA25反转信号", symbol);
                } else {
                    // 🔥 价格距离保护：两次减仓之间价格必须变化≥2%
                    boolean priceDistanceOk = checkPriceDistanceForReduce(longPos, currentPrice);

                    if (!priceDistanceOk) {
                        logger.debug("[{}] 多单价格距离不足2%，跳过减仓", symbol);
                    } else if (!canExecuteHedgeOperation(symbol, "LONG_HIGH_PROFIT", 2)) { // 2分钟冷却
                        logger.debug("[{}] 多单高盈利减仓冷却中，跳过", symbol);
                    } else {
                        // 🔥 新增：阻力位检测逻辑
                        boolean nearResistance = false;
                        BigDecimal resistancePrice = null;
                        BigDecimal resistanceDistance = BigDecimal.ZERO;

                        logger.info("[{}] 📊 RESISTANCE_DETECTION_START - 开始检测阻力位 | 多单盈利{}%", symbol, longPnlPercentage.toPlainString());

                        try {
                            resistancePrice = supportResistanceDetector.getLatestResistance(symbol, "4h", currentPrice);

                            if (resistancePrice != null && resistancePrice.compareTo(BigDecimal.valueOf(Double.MAX_VALUE)) < 0) {
                                // 计算距离阻力位的百分比
                                resistanceDistance = resistancePrice.subtract(currentPrice).divide(currentPrice, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).abs();

                                // 判断是否接近阻力位（±5%范围内，扩大范围增加触发概率）
                                BigDecimal resistanceRange = resistancePrice.multiply(new BigDecimal("0.05")); // 5%范围
                                nearResistance = currentPrice.subtract(resistancePrice).abs().compareTo(resistanceRange) <= 0;

                                logger.info("[{}] 📊 RESISTANCE_DETECTION_RESULT - 阻力位检测完成 | 阻力位: {} | 当前价格: {} | 距离: {}% | 接近阻力位: {}",
                                           symbol, resistancePrice.toPlainString(), currentPrice.toPlainString(),
                                           resistanceDistance.toPlainString(), nearResistance ? "是" : "否");
                            } else {
                                logger.info("[{}] 📊 RESISTANCE_DETECTION_NONE - 未检测到有效阻力位，跳过阻力位减仓", symbol);
                            }
                        } catch (Exception e) {
                            logger.warn("[{}] 📊 RESISTANCE_DETECTION_ERROR - 阻力位检测异常: {}", symbol, e.getMessage());
                        }

                        // 🔥 核心逻辑：只有接近阻力位时才减仓
                        if (!nearResistance) {
                            logger.info("[{}] 📊 RESISTANCE_SKIP - 多单盈利{}%但未接近阻力位，跳过减仓", symbol, longPnlPercentage.toPlainString());
                        } else {
                            // 🔥 新增：检查对冲单（空单）亏损情况
                            boolean shouldHedgeAdd = false;
                            BigDecimal shortLossPercentage = BigDecimal.ZERO;

                            if (shortPos != null && shortPos.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                                BigDecimal shortPnlForCheck = shortPos.calculatePnl(currentPrice);
                                shortLossPercentage = shortPnlForCheck.divide(shortPos.getTotalAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));

                                // 只有空单亏损大于5%才考虑对冲加仓
                                shouldHedgeAdd = shortLossPercentage.compareTo(new BigDecimal("-5.0")) < 0;

                                logger.info("[{}] 📊 HEDGE_LOSS_CHECK - 对冲单检查 | 空单亏损: {}% | 是否需要对冲加仓: {}",
                                           symbol, shortLossPercentage.toPlainString(), shouldHedgeAdd ? "是" : "否");
                            } else {
                                logger.info("[{}] 📊 HEDGE_LOSS_CHECK - 无空单持仓，跳过对冲加仓检查", symbol);
                            }

                            // 🔥 检查是否小额持仓（<5U），如果是则完全平仓（市价单）
                            if (longPos.getTotalAmount().compareTo(new BigDecimal("5.0")) < 0) {
                                logger.info("[{}] 🎯 HEDGE_LONG_SMALL_RESISTANCE - 多单小额持仓{}U，盈利{}%，接近阻力位{}，市价完全平仓",
                                           symbol, longPos.getTotalAmount().toPlainString(), longPnlPercentage.toPlainString(),
                                           resistancePrice != null ? resistancePrice.toPlainString() : "N/A");

                                boolean closed = tradingService.closePosition(symbol, "SELL", BigDecimal.ONE, "HEDGE_LONG_SMALL_RESISTANCE_CLOSE");
                                if (closed) {
                                    positionSizingService.recordTradeProfit(symbol, longPnl, "HEDGE_LONG_SMALL_RESISTANCE");
                                    recordHedgeOperation(symbol, "LONG_SMALL_RESISTANCE");
                                    return true;
                                }
                            } else {
                                // 🔥 新增：检查是否已有止盈限价单，避免重复挂单
                                List<TradeRecord> existingProfitOrders = tradingService.getPendingProfitOrders(symbol, "LONG");
                                if (!existingProfitOrders.isEmpty()) {
                                    logger.info("[{}] 💰 多单已有{}个止盈限价单待成交，跳过重复挂单", symbol, existingProfitOrders.size());
                                    return false;
                                }

                                // 🔥 检查价格冲突（1%容忍度）
                                if (tradingService.hasPriceConflict(existingProfitOrders, resistancePrice, 1.0)) {
                                    logger.info("[{}] 阻力位减仓跳过：已有相近价格{}的止盈单", symbol, resistancePrice.toPlainString());
                                    return false;
                                }

                                logger.info("[{}] 🎯 HEDGE_LONG_RESISTANCE_PROFIT - 多单盈利{}%+接近阻力位{}，距离{}%，挂限价止盈单",
                                           symbol, longPnlPercentage.toPlainString(),
                                           resistancePrice != null ? resistancePrice.toPlainString() : "N/A",
                                           resistanceDistance.toPlainString());

                                // 🔥 使用限价单减仓50%
                                BigDecimal profitQuantity = longPos.getQuantity().multiply(new BigDecimal("0.5"));
                                String profitOrderId = tradingService.placeProfitLimitOrder(symbol, "SELL", profitQuantity,
                                                                                          resistancePrice, "LONG", "HEDGE_LONG_RESISTANCE_PROFIT");

                                if (profitOrderId == null) {
                                    logger.error("[{}] 阻力位止盈限价单下单失败", symbol);
                                    return false;
                                }

                                logger.info("[{}] 🎯 阻力位止盈单已挂：多单减仓50%，价格{}，订单ID:{}",
                                           symbol, resistancePrice.toPlainString(), profitOrderId);

                                BigDecimal reducedAmount = longPos.getTotalAmount().multiply(new BigDecimal("0.5"));

                                // 🔥 新增：只有在对冲单亏损大于5%时才进行25%对冲加仓（限价单）
                                if (reducedAmount.compareTo(BigDecimal.ZERO) > 0 && shouldHedgeAdd) {
                                    BigDecimal hedgeAddValue = reducedAmount.multiply(new BigDecimal("0.5")); // 减仓的50%
                                    BigDecimal finalHedgeAddValue = checkAndAdjustPositionSize(symbol, currentPrice, hedgeAddValue, "阻力位对冲平衡加仓空单");

                                    // 🔥 新增：检查最小订单金额限制（5U）
                                    if (finalHedgeAddValue.compareTo(new BigDecimal("5.0")) >= 0) {
                                        // 计算加仓数量
                                        BigDecimal addQuantity = finalHedgeAddValue.divide(resistancePrice, tradingService.getQuantityPrecision(symbol), RoundingMode.HALF_UP);

                                        logger.info("[{}] 🔄 HEDGE_RESISTANCE_BALANCE_ADD - 阻力位减仓多单{}U后，空单亏损{}%>5%，挂限价加仓空单{}U(25%)",
                                                   symbol, reducedAmount.toPlainString(), shortLossPercentage.toPlainString(), finalHedgeAddValue.toPlainString());

                                        String hedgeOrderId = tradingService.addPositionLimitOrder(symbol, "SELL", addQuantity, resistancePrice, "SHORT", "HEDGE_RESISTANCE_BALANCE_SHORT");
                                        if (hedgeOrderId != null) {
                                            logger.info("[{}] 🎉 HEDGE_RESISTANCE_BALANCE_SUCCESS - 阻力位对冲平衡限价加仓成功，空单加仓{}U，价格{}，订单ID:{}",
                                                       symbol, finalHedgeAddValue.toPlainString(), resistancePrice.toPlainString(), hedgeOrderId);
                                        } else {
                                            logger.warn("[{}] ⚠️ HEDGE_RESISTANCE_BALANCE_FAILED - 阻力位对冲平衡限价加仓失败", symbol);
                                        }
                                    } else {
                                        logger.info("[{}] 🔄 HEDGE_RESISTANCE_BALANCE_SKIP - 阻力位对冲平衡加仓金额{}U < 5U，跳过加仓",
                                                   symbol, finalHedgeAddValue.toPlainString());
                                    }
                                } else if (reducedAmount.compareTo(BigDecimal.ZERO) > 0 && !shouldHedgeAdd) {
                                    logger.info("[{}] 🔄 HEDGE_RESISTANCE_NO_ADD - 阻力位减仓多单{}U，但空单亏损{}%<5%，跳过对冲加仓",
                                               symbol, reducedAmount.toPlainString(), shortLossPercentage.toPlainString());
                                }
                            
                            // 🔥 记录减仓价格、时间和次数
                            longPos.setLastReducePrice(currentPrice);
                            longPos.setLastReduceTime(LocalDateTime.now());
                            longPos.setReduceCount((longPos.getReduceCount() != null ? longPos.getReduceCount() : 0) + 1);
                                // 🔥 修复：对冲减仓也设置hasProfitReduced标记，确保只减仓一次
                                longPos.setHasProfitReduced(true);
                            positionRepository.save(longPos);
                            
                            positionSizingService.recordTradeProfit(symbol, longPnl, "HEDGE_LONG_RESISTANCE_PROFIT");
                            recordHedgeOperation(symbol, "LONG_RESISTANCE_PROFIT");
                            return true;
                        }
                    }
                }
            }
            } else if (longPnlPercentage.compareTo(new BigDecimal("5.0")) > 0 && longPnl.compareTo(BigDecimal.ZERO) <= 0) {
                logger.debug("[{}] 多单虽然满足盈利百分比但实际亏损，跳过高盈利减仓", symbol);
            }
            
            // 🔥 策略3：空单高盈利+支撑位减仓（集成支撑位检查 + 对冲单亏损检查）
            if (shortPnlPercentage.compareTo(new BigDecimal("5.0")) > 0 && shortPnl.compareTo(BigDecimal.ZERO) > 0) { // 🔥 新增：必须盈利才能减仓
                // 🔥 修复：优先检查是否已经减仓过，确保"只减仓一次"逻辑
                if (shortPos.isHasProfitReduced()) {
                    logger.debug("[{}] 空单已进行过盈利减仓，跳过高盈利减仓，等待MA25反转信号", symbol);
                } else {
                    // 🔥 价格距离保护：两次减仓之间价格必须变化≥2%
                    boolean priceDistanceOk = checkPriceDistanceForReduce(shortPos, currentPrice);

                    if (!priceDistanceOk) {
                        logger.debug("[{}] 空单价格距离不足2%，跳过减仓", symbol);
                    } else if (!canExecuteHedgeOperation(symbol, "SHORT_HIGH_PROFIT", 2)) { // 2分钟冷却
                        logger.debug("[{}] 空单高盈利减仓冷却中，跳过", symbol);
                    } else {
                        // 🔥 新增：支撑位检测逻辑
                        boolean nearSupport = false;
                        BigDecimal supportPrice = null;
                        BigDecimal supportDistance = BigDecimal.ZERO;

                        logger.info("[{}] 📊 SUPPORT_DETECTION_START - 开始检测支撑位 | 空单盈利{}%", symbol, shortPnlPercentage.toPlainString());

                        try {
                            supportPrice = supportResistanceDetector.getLatestSupport(symbol, "4h", currentPrice);

                            if (supportPrice != null && supportPrice.compareTo(BigDecimal.ZERO) > 0) {
                                // 计算距离支撑位的百分比
                                supportDistance = currentPrice.subtract(supportPrice).divide(currentPrice, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).abs();

                                // 判断是否接近支撑位（±5%范围内，扩大范围增加触发概率）
                                BigDecimal supportRange = supportPrice.multiply(new BigDecimal("0.05")); // 5%范围
                                nearSupport = currentPrice.subtract(supportPrice).abs().compareTo(supportRange) <= 0;

                                logger.info("[{}] 📊 SUPPORT_DETECTION_RESULT - 支撑位检测完成 | 支撑位: {} | 当前价格: {} | 距离: {}% | 接近支撑位: {}",
                                           symbol, supportPrice.toPlainString(), currentPrice.toPlainString(),
                                           supportDistance.toPlainString(), nearSupport ? "是" : "否");
                            } else {
                                logger.info("[{}] 📊 SUPPORT_DETECTION_NONE - 未检测到有效支撑位，跳过支撑位减仓", symbol);
                            }
                        } catch (Exception e) {
                            logger.warn("[{}] 📊 SUPPORT_DETECTION_ERROR - 支撑位检测异常: {}", symbol, e.getMessage());
                        }

                        // 🔥 核心逻辑：只有接近支撑位时才减仓
                        if (!nearSupport) {
                            logger.info("[{}] 📊 SUPPORT_SKIP - 空单盈利{}%但未接近支撑位，跳过减仓", symbol, shortPnlPercentage.toPlainString());
                        } else {
                            // 🔥 新增：检查对冲单（多单）亏损情况
                            boolean shouldHedgeAdd = false;
                            BigDecimal longLossPercentage = BigDecimal.ZERO;

                            if (longPos != null && longPos.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                                BigDecimal longPnlForCheck = longPos.calculatePnl(currentPrice);
                                longLossPercentage = longPnlForCheck.divide(longPos.getTotalAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));

                                // 只有多单亏损大于5%才考虑对冲加仓
                                shouldHedgeAdd = longLossPercentage.compareTo(new BigDecimal("-5.0")) < 0;

                                logger.info("[{}] 📊 HEDGE_LOSS_CHECK - 对冲单检查 | 多单亏损: {}% | 是否需要对冲加仓: {}",
                                           symbol, longLossPercentage.toPlainString(), shouldHedgeAdd ? "是" : "否");
                            } else {
                                logger.info("[{}] 📊 HEDGE_LOSS_CHECK - 无多单持仓，跳过对冲加仓检查", symbol);
                            }

                            // 🔥 检查是否小额持仓（<5U），如果是则完全平仓（市价单）
                            if (shortPos.getTotalAmount().compareTo(new BigDecimal("5.0")) < 0) {
                                logger.info("[{}] 🎯 HEDGE_SHORT_SMALL_SUPPORT - 空单小额持仓{}U，盈利{}%，接近支撑位{}，市价完全平仓",
                                           symbol, shortPos.getTotalAmount().toPlainString(), shortPnlPercentage.toPlainString(),
                                           supportPrice != null ? supportPrice.toPlainString() : "N/A");

                                boolean closed = tradingService.closePosition(symbol, "BUY", BigDecimal.ONE, "HEDGE_SHORT_SMALL_SUPPORT_CLOSE");
                                if (closed) {
                                    positionSizingService.recordTradeProfit(symbol, shortPnl, "HEDGE_SHORT_SMALL_SUPPORT");
                                    recordHedgeOperation(symbol, "SHORT_SMALL_SUPPORT");
                                    return true;
                                }
                            } else {
                                // 🔥 新增：检查是否已有止盈限价单，避免重复挂单
                                List<TradeRecord> existingProfitOrders = tradingService.getPendingProfitOrders(symbol, "SHORT");
                                if (!existingProfitOrders.isEmpty()) {
                                    logger.info("[{}] 💰 空单已有{}个止盈限价单待成交，跳过重复挂单", symbol, existingProfitOrders.size());
                                    return false;
                                }

                                // 🔥 检查价格冲突（1%容忍度）
                                if (tradingService.hasPriceConflict(existingProfitOrders, supportPrice, 1.0)) {
                                    logger.info("[{}] 支撑位减仓跳过：已有相近价格{}的止盈单", symbol, supportPrice.toPlainString());
                                    return false;
                                }

                                logger.info("[{}] 🎯 HEDGE_SHORT_SUPPORT_PROFIT - 空单盈利{}%+接近支撑位{}，距离{}%，挂限价止盈单",
                                           symbol, shortPnlPercentage.toPlainString(),
                                           supportPrice != null ? supportPrice.toPlainString() : "N/A",
                                           supportDistance.toPlainString());

                                // 🔥 使用限价单减仓50%
                                BigDecimal profitQuantity = shortPos.getQuantity().multiply(new BigDecimal("0.5"));
                                String profitOrderId = tradingService.placeProfitLimitOrder(symbol, "BUY", profitQuantity,
                                                                                          supportPrice, "SHORT", "HEDGE_SHORT_SUPPORT_PROFIT");

                                if (profitOrderId == null) {
                                    logger.error("[{}] 支撑位止盈限价单下单失败", symbol);
                                    return false;
                                }

                                logger.info("[{}] 🎯 支撑位止盈单已挂：空单减仓50%，价格{}，订单ID:{}",
                                           symbol, supportPrice.toPlainString(), profitOrderId);

                                BigDecimal reducedAmount = shortPos.getTotalAmount().multiply(new BigDecimal("0.5"));

                                // 🔥 新增：只有在对冲单亏损大于5%时才进行25%对冲加仓（限价单）
                                if (reducedAmount.compareTo(BigDecimal.ZERO) > 0 && shouldHedgeAdd) {
                                    BigDecimal hedgeAddValue = reducedAmount.multiply(new BigDecimal("0.5")); // 减仓的50%
                                    BigDecimal finalHedgeAddValue = checkAndAdjustPositionSize(symbol, currentPrice, hedgeAddValue, "支撑位对冲平衡加仓多单");

                                    // 🔥 新增：检查最小订单金额限制（5U）
                                    if (finalHedgeAddValue.compareTo(new BigDecimal("5.0")) >= 0) {
                                        // 计算加仓数量
                                        BigDecimal addQuantity = finalHedgeAddValue.divide(supportPrice, tradingService.getQuantityPrecision(symbol), RoundingMode.HALF_UP);

                                        logger.info("[{}] 🔄 HEDGE_SUPPORT_BALANCE_ADD - 支撑位减仓空单{}U后，多单亏损{}%>5%，挂限价加仓多单{}U(25%)",
                                                   symbol, reducedAmount.toPlainString(), longLossPercentage.toPlainString(), finalHedgeAddValue.toPlainString());

                                        String hedgeOrderId = tradingService.addPositionLimitOrder(symbol, "BUY", addQuantity, supportPrice, "LONG", "HEDGE_SUPPORT_BALANCE_LONG");
                                        if (hedgeOrderId != null) {
                                            logger.info("[{}] 🎉 HEDGE_SUPPORT_BALANCE_SUCCESS - 支撑位对冲平衡限价加仓成功，多单加仓{}U，价格{}，订单ID:{}",
                                                       symbol, finalHedgeAddValue.toPlainString(), supportPrice.toPlainString(), hedgeOrderId);
                                        } else {
                                            logger.warn("[{}] ⚠️ HEDGE_SUPPORT_BALANCE_FAILED - 支撑位对冲平衡限价加仓失败", symbol);
                                        }
                                    } else {
                                        logger.info("[{}] 🔄 HEDGE_SUPPORT_BALANCE_SKIP - 支撑位对冲平衡加仓金额{}U < 5U，跳过加仓",
                                                   symbol, finalHedgeAddValue.toPlainString());
                                    }
                                } else if (reducedAmount.compareTo(BigDecimal.ZERO) > 0 && !shouldHedgeAdd) {
                                    logger.info("[{}] 🔄 HEDGE_SUPPORT_NO_ADD - 支撑位减仓空单{}U，但多单亏损{}%<5%，跳过对冲加仓",
                                               symbol, reducedAmount.toPlainString(), longLossPercentage.toPlainString());
                                }
                            
                            // 🔥 记录减仓价格、时间和次数
                            shortPos.setLastReducePrice(currentPrice);
                            shortPos.setLastReduceTime(LocalDateTime.now());
                            shortPos.setReduceCount((shortPos.getReduceCount() != null ? shortPos.getReduceCount() : 0) + 1);
                                // 🔥 修复：对冲减仓也设置hasProfitReduced标记，确保只减仓一次
                                shortPos.setHasProfitReduced(true);
                            positionRepository.save(shortPos);
                            
                                positionSizingService.recordTradeProfit(symbol, shortPnl, "HEDGE_SHORT_SUPPORT_PROFIT");
                                recordHedgeOperation(symbol, "SHORT_SUPPORT_PROFIT");
                                return true;
                            }
                        }
                    }
                }
            } else if (shortPnlPercentage.compareTo(new BigDecimal("5.0")) > 0 && shortPnl.compareTo(BigDecimal.ZERO) <= 0) {
                logger.debug("[{}] 空单虽然满足盈利百分比但实际亏损，跳过高盈利减仓", symbol);
            }
            
            // 🔥 策略4：对冲失效保护已移除 - 遵循"亏损时只加仓不止损"原则
            // 对冲状态下亏损时，通过50U单仓止损和加仓策略处理，不设额外止损
            
            // 🔥 策略5：双边MA25反转兜底（最后防线）
            String longProfitSignal = ma25TradingStrategy.checkLongProfitSignal(symbol, longPos);
            String shortProfitSignal = ma25TradingStrategy.checkShortProfitSignal(symbol, shortPos);
            
            if ("MA25_BREAK".equals(longProfitSignal) && "MA25_BREAK".equals(shortProfitSignal)) {
                if (!canExecuteHedgeOperation(symbol, "DOUBLE_MA25_BREAK", 5)) {
                    return false; // 5分钟冷却
                }
                
                logger.info("[{}] 🎯 HEDGE_DOUBLE_MA25_BREAK - 双边MA25反转兜底，整体止盈", symbol);
                executeFullHedgeClose(symbol, "DOUBLE_MA25_BREAK", totalPnl);
                recordHedgeOperation(symbol, "DOUBLE_MA25_BREAK");
                return true;
            }
            
            // 🔥 策略6：正常运行，继续持有
            logger.debug("[{}] 🔄 HEDGE_NORMAL - 对冲运行正常，净盈亏{}%，继续持有", symbol, totalPnlPercentage.toPlainString());
            return false;
            
        } catch (Exception e) {
            logger.error("[{}] 灵活对冲止盈策略异常: {}", symbol, e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取动态减仓阈值（递增：4% → 6% → 8% → 10%）
     */
    private BigDecimal getDynamicReduceThreshold(Position position) {
        return position.getDynamicReduceThreshold();
    }
    
    /**
     * 检查价格距离是否满足减仓条件（≥2%）
     */
    private boolean checkPriceDistanceForReduce(Position position, BigDecimal currentPrice) {
        BigDecimal distance = position.getDistanceFromLastReduce(currentPrice);
        return distance.compareTo(new BigDecimal("2.0")) >= 0; // 价格变化≥2%
    }
    
    /**
     * 执行完全对冲平仓（同时平掉多空）
     */
    private void executeFullHedgeClose(String symbol, String reason, BigDecimal totalPnl) {
        try {
            boolean longClosed = tradingService.closePosition(symbol, "SELL", BigDecimal.ONE, reason + "_LONG");
            boolean shortClosed = tradingService.closePosition(symbol, "BUY", BigDecimal.ONE, reason + "_SHORT");
            
            if (longClosed && shortClosed) {
                String pnlSign = totalPnl.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "";
                logger.info("[{}] 🎉 HEDGE_FULL_CLOSE_SUCCESS - 完全对冲平仓成功！净盈亏: {}{}U | 原因: {}", 
                           symbol, pnlSign, totalPnl.toPlainString(), reason);
                positionSizingService.recordTradeProfit(symbol, totalPnl, reason);
                
                // 🔥 生生不息：对冲平仓后暂不重新开仓，让市场消化
                logger.info("[{}] 🔄 对冲平仓完成，暂不重新开仓，让市场消化", symbol);
            } else {
                logger.error("[{}] HEDGE_FULL_CLOSE_FAILED - 完全对冲平仓失败：多单={}, 空单={}", 
                            symbol, longClosed, shortClosed);
            }
        } catch (Exception e) {
            logger.error("[{}] 执行完全对冲平仓异常: {}", symbol, e.getMessage());
        }
    }

    /**
     * 🔥 新增：安全取消限价单，忽略订单不存在的错误
     * @param symbol 交易对
     * @param orderId 订单ID
     */
    private void safeCancelLimitOrder(String symbol, String orderId) {
        int maxRetries = 2; // 最大重试次数
        int retryCount = 0;
        
        while (retryCount < maxRetries) {
            try {
                boolean success = tradingService.cancelLimitOrder(symbol, orderId);
                if (success) {
                    logger.info("[{}] 限价单{}取消成功", symbol, orderId);
                    
                    // 🔥 新增：更新本地订单状态为CANCELED
                    updateLocalOrderStatus(orderId, "CANCELED");
                    return;
                } else {
                    logger.debug("[{}] 限价单{}取消失败，可能已不存在", symbol, orderId);
                    // 🔥 新增：标记为已取消状态
                    updateLocalOrderStatus(orderId, "CANCELED");
                    return;
                }
            } catch (Exception e) {
                String errorMsg = e.getMessage();
                if (errorMsg != null) {
                    if (errorMsg.contains("Unknown order") || errorMsg.contains("ORDER_NOT_EXIST") || errorMsg.contains("-2011")) {
                        logger.info("[{}] 限价单{}已不存在，无需取消", symbol, orderId);
                        // 🔥 新增：标记为已取消状态
                        updateLocalOrderStatus(orderId, "CANCELED");
                        return;
                    } else if (errorMsg.contains("timeout") || errorMsg.contains("OKHTTP") || errorMsg.contains("-1021")) {
                        // 网络超时或时间戳问题，可以重试
                        retryCount++;
                        if (retryCount < maxRetries) {
                            logger.warn("[{}] 取消限价单{}网络错误，第{}次重试: {}", symbol, orderId, retryCount, errorMsg);
                            try {
                                Thread.sleep(1000 * retryCount); // 递增延迟重试
                            } catch (InterruptedException ie) {
                                Thread.currentThread().interrupt();
                                break;
                            }
                            continue;
                        } else {
                            logger.error("[{}] 取消限价单{}达到最大重试次数，放弃操作: {}", symbol, orderId, errorMsg);
                            return;
                        }
                    } else {
                        logger.warn("[{}] 取消限价单{}未知异常: {}", symbol, orderId, errorMsg);
                        return;
                    }
                } else {
                    logger.warn("[{}] 取消限价单{}异常，错误信息为空", symbol, orderId);
                    return;
                }
            }
        }
    }
    
    /**
     * 🔥 新增：更新本地订单状态
     * @param orderId 订单ID
     * @param newStatus 新状态
     */
    private void updateLocalOrderStatus(String orderId, String newStatus) {
        try {
            Optional<TradeRecord> recordOpt = tradeRecordRepository.findByOrderId(orderId);
            if (recordOpt.isPresent()) {
                TradeRecord record = recordOpt.get();
                String oldStatus = record.getStatus();
                record.setStatus(newStatus);
                record.setUpdatedAt(LocalDateTime.now());
                tradeRecordRepository.save(record);
                logger.debug("订单{}状态更新：{} -> {}", orderId, oldStatus, newStatus);
            }
        } catch (Exception e) {
            logger.warn("更新订单{}状态异常: {}", orderId, e.getMessage());
        }
    }
    
    /**
     * 🔥 优化：智能检测支撑阻力位（带缓存机制，5分钟内不重复检测）
     */
    private void smartCheckSupportResistanceLevels(String symbol, BigDecimal currentPrice, String reason) {
        try {
            // 检查是否需要更新缓存（5分钟内不重复检测）
            LocalDateTime lastCheck = lastLevelCheckTime.get(symbol);
            LocalDateTime now = LocalDateTime.now();
            
            if (lastCheck != null && java.time.Duration.between(lastCheck, now).toMinutes() < 5) {
                logger.debug("[{}] 支撑阻力位检测缓存有效，跳过重复检测 | 原因: {}", symbol, reason);
                return;
            }
            
            // 更新检测时间
            lastLevelCheckTime.put(symbol, now);
            
            // 执行检测
            logger.debug("[{}] 执行支撑阻力位检测 | 原因: {} | 距离上次检测: {}分钟", 
                        symbol, reason, lastCheck != null ? java.time.Duration.between(lastCheck, now).toMinutes() : "首次");
            checkAndLogSupportResistanceLevels(symbol, currentPrice);
            
        } catch (Exception e) {
            logger.error("[{}] 智能支撑阻力位检测异常: {}", symbol, e.getMessage());
        }
    }

    /**
     * 🔥 新增：调试方法，用于测试对冲平衡加仓的价格距离逻辑
     * @param symbol 交易对
     * @param positionSide 持仓方向
     */
    public void debugHedgeBalancePriceDistance(String symbol, String positionSide) {
        try {
            Optional<Position> positionOpt = tradingService.getActivePosition(symbol, positionSide);
            if (positionOpt.isPresent()) {
                Position position = positionOpt.get();
                BigDecimal currentPrice = tradingService.getCurrentPrice(symbol);
                BigDecimal minDistance = position.getMinDistanceFromHistory(currentPrice);
                BigDecimal requiredDistance = new BigDecimal("2.0"); // 保持原有的2%要求
                
                logger.info("[{}] 🔍 对冲平衡价格距离调试 - {}持仓:", symbol, positionSide);
                logger.info("[{}]    ├─ 当前价格: {}", symbol, currentPrice.toPlainString());
                logger.info("[{}]    ├─ 最小距离: {}%", symbol, minDistance.toPlainString());
                logger.info("[{}]    ├─ 要求距离: 2%", symbol);
                logger.info("[{}]    ├─ 有对冲加仓: {}", symbol, position.isHasHedgeBalanceAdd());
                logger.info("[{}]    ├─ 加仓历史: {}", symbol, position.getAddPriceHistory());
                logger.info("[{}]    └─ 可以加仓: {}", symbol, minDistance.compareTo(requiredDistance) >= 0);
            }
        } catch (Exception e) {
            logger.error("[{}] 调试对冲平衡价格距离异常: {}", symbol, e.getMessage());
        }
    }

    // 🔥 已删除：未使用的智能减仓代码，简化代码结构
}


